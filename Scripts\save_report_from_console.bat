@echo off
echo.
echo 📄 DNS Security Report Saver
echo ============================
echo.
echo This script helps you save DNS security reports from N8N console output.
echo.

REM Create reports directory if it doesn't exist
if not exist "C:\DNS_Security_Reports" (
    mkdir "C:\DNS_Security_Reports"
    echo ✅ Created directory: C:\DNS_Security_Reports
    echo.
)

echo 📋 Instructions:
echo.
echo 1. Run your N8N DNS Security workflow
echo 2. Copy the HTML content from the console logs
echo    (Look for content between the ======== lines)
echo 3. Paste it into a text file
echo 4. Save the file with .html extension in C:\DNS_Security_Reports\
echo 5. Double-click the .html file to open in browser
echo.
echo 🌐 Example file names:
echo    - dns_security_report_2025-08-15.html
echo    - DNS_SECURITY_ALERT_2025-08-15_123456.txt
echo.
echo 💡 Quick PowerShell method:
echo    1. Copy HTML content from N8N logs
echo    2. Open PowerShell in C:\DNS_Security_Reports\
echo    3. Run: Get-Clipboard ^| Out-File -FilePath "report.html" -Encoding UTF8
echo    4. Run: Start-Process "report.html"
echo.

set /p choice="Open reports directory now? (y/N): "
if /i "%choice%"=="y" (
    explorer "C:\DNS_Security_Reports"
    echo ✅ Opened reports directory
) else (
    echo 📁 Reports directory: C:\DNS_Security_Reports
)

echo.
echo 🔗 To view reports in browser, use URLs like:
echo    file:///C:/DNS_Security_Reports/dns_security_report_2025-08-15.html
echo.
pause
