#!/usr/bin/env python3
"""
Test script for MCP Research functionality
Tests the N8N documentation search capabilities
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import n8n_builder modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from n8n_builder.mcp_research_tool import N8NResearchTool
from n8n_builder.config import config

async def test_research_tool():
    """Test the N8N research tool functionality."""
    print("🔍 Testing N8N MCP Research Tool")
    print("=" * 50)
    
    # Check GitHub API token
    github_token = os.getenv("GITHUB_API_TOKEN")
    if github_token:
        print(f"✅ GitHub API token found (length: {len(github_token)})")
    else:
        print("⚠️  No GitHub API token found - using unauthenticated requests")
    
    # Initialize research tool
    try:
        research_tool = N8NResearchTool(
            cache_ttl=300,  # 5 minutes for testing
            enable_enhanced_cache=False,  # Disable for testing
            github_api_token=github_token
        )
        print("✅ Research tool initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize research tool: {e}")
        return
    
    # Test cases
    test_cases = [
        {
            "name": "HTTP Request Node",
            "query": "HTTP Request node documentation",
            "node_name": "HTTP Request"
        },
        {
            "name": "Webhook Node", 
            "query": "webhook trigger documentation",
            "node_name": "Webhook"
        },
        {
            "name": "General Workflow",
            "query": "workflow automation best practices",
            "node_name": None
        }
    ]
    
    async with research_tool:
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🧪 Test {i}: {test_case['name']}")
            print("-" * 30)
            
            try:
                # Test the search functionality
                results = await research_tool.search_n8n_docs(
                    query=test_case["query"],
                    node_type=test_case["node_name"]
                )
                
                if results:
                    print(f"✅ Found {len(results)} results")
                    for j, result in enumerate(results[:2], 1):  # Show first 2 results
                        print(f"  {j}. {result.title}")
                        print(f"     Source: {result.source}")
                        print(f"     URL: {result.url}")
                        print(f"     Relevance: {result.relevance_score:.2f}")
                        print(f"     Content preview: {result.content[:100]}...")
                        print()
                else:
                    print("❌ No results found")
                    
            except Exception as e:
                print(f"❌ Test failed: {e}")
                import traceback
                traceback.print_exc()
    
    print("\n🏁 Testing completed")

async def test_documentation_targets():
    """Test the documentation target generation."""
    print("\n🎯 Testing Documentation Targets")
    print("=" * 50)
    
    research_tool = N8NResearchTool()
    
    test_queries = [
        ("HTTP Request node", "HTTP Request"),
        ("webhook trigger", None),
        ("email notification", None),
        ("database connection", None)
    ]
    
    for query, node_type in test_queries:
        print(f"\nQuery: '{query}' (Node: {node_type})")
        targets = research_tool._get_documentation_targets(query, node_type)
        
        print(f"Generated {len(targets)} targets:")
        for target in targets:
            print(f"  - {target['title']} ({target['source']})")
            print(f"    Path: {target['path']}")

if __name__ == "__main__":
    print("🚀 Starting MCP Research Tests")
    
    # Test documentation targets first (synchronous)
    asyncio.run(test_documentation_targets())
    
    # Test actual research functionality (asynchronous)
    asyncio.run(test_research_tool())
