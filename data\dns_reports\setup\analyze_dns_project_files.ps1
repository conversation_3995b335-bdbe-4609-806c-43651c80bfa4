# Analyze DNS Security Project Files and Dependencies
# This script identifies all DNS-related files and their path references to help centralize the project

Write-Host "DNS Security Project File Analysis" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host ""

$dnsFiles = @()
$pathReferences = @()

# Define DNS-related file patterns
$dnsPatterns = @(
    "*dns*",
    "*domain*"
)

# Search for DNS-related files in key directories
$searchDirs = @(
    "Scripts",
    "data\dns_reports",
    "Documentation"
)

Write-Host "Searching for DNS-related files..." -ForegroundColor Yellow

foreach ($dir in $searchDirs) {
    if (Test-Path $dir) {
        Write-Host "  Searching in: $dir" -ForegroundColor Gray
        
        foreach ($pattern in $dnsPatterns) {
            $files = Get-ChildItem -Path $dir -Filter $pattern -Recurse -File -ErrorAction SilentlyContinue
            foreach ($file in $files) {
                $dnsFiles += [PSCustomObject]@{
                    FullPath = $file.FullName
                    RelativePath = $file.FullName.Replace((Get-Location).Path + "\", "")
                    Directory = $file.Directory.Name
                    Name = $file.Name
                    Size = $file.Length
                    LastModified = $file.LastWriteTime
                }
                Write-Host "    Found: $($file.Name)" -ForegroundColor Green
            }
        }
    }
}

Write-Host ""
Write-Host "DNS-Related Files Found:" -ForegroundColor Cyan
$dnsFiles | Sort-Object RelativePath | Format-Table RelativePath, Name, Size, LastModified -AutoSize

Write-Host ""
Write-Host "Analyzing path references in DNS files..." -ForegroundColor Yellow

foreach ($file in $dnsFiles) {
    if ($file.Name -match "\.(ps1|bat|md|json)$") {
        Write-Host "  Analyzing: $($file.Name)" -ForegroundColor Gray
        
        try {
            $content = Get-Content $file.FullPath -Raw -ErrorAction SilentlyContinue
            if ($content) {
                # Look for path references
                $pathMatches = @()
                
                # PowerShell script references
                $pathMatches += [regex]::Matches($content, 'Scripts\\[^"\s\)]+') | ForEach-Object { $_.Value }
                $pathMatches += [regex]::Matches($content, 'data\\[^"\s\)]+') | ForEach-Object { $_.Value }
                $pathMatches += [regex]::Matches($content, 'Documentation\\[^"\s\)]+') | ForEach-Object { $_.Value }

                # File path references in quotes
                $pathMatches += [regex]::Matches($content, '"[^"]*\\[^"]*"') | ForEach-Object { $_.Value.Trim('"') }

                # Relative path references
                $pathMatches += [regex]::Matches($content, '\.\\\w+\\[^\s\)]+') | ForEach-Object { $_.Value }
                
                foreach ($match in $pathMatches | Sort-Object -Unique) {
                    if ($match -and $match.Length -gt 3) {
                        $pathReferences += [PSCustomObject]@{
                            SourceFile = $file.RelativePath
                            ReferencedPath = $match
                            Type = if ($match -match "Scripts\\") { "Script" } 
                                   elseif ($match -match "data\\") { "Data" }
                                   elseif ($match -match "Documentation\\") { "Documentation" }
                                   else { "Other" }
                        }
                    }
                }
            }
        } catch {
            Write-Host "    Warning: Could not analyze $($file.Name): $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
}

Write-Host ""
Write-Host "Path References Analysis:" -ForegroundColor Cyan
$pathReferences | Sort-Object SourceFile, ReferencedPath | Format-Table SourceFile, ReferencedPath, Type -AutoSize

Write-Host ""
Write-Host "Centralization Analysis:" -ForegroundColor Cyan
Write-Host ""

# Identify files that need to be moved
$filesToMove = @()
$pathUpdatesNeeded = @()

# Files currently in Scripts that could be moved to setup
$scriptsFiles = $dnsFiles | Where-Object { $_.RelativePath -match "^Scripts\\" }
Write-Host "Files currently in Scripts folder:" -ForegroundColor Yellow
foreach ($file in $scriptsFiles) {
    Write-Host "  - $($file.RelativePath)" -ForegroundColor White
    $filesToMove += [PSCustomObject]@{
        CurrentPath = $file.RelativePath
        ProposedPath = $file.RelativePath -replace "^Scripts\\", "data\dns_reports\setup\"
        Reason = "Centralize DNS project files"
    }
}

Write-Host ""
Write-Host "Proposed File Moves:" -ForegroundColor Green
$filesToMove | Format-Table CurrentPath, ProposedPath, Reason -AutoSize

Write-Host ""
Write-Host "Path Updates Required:" -ForegroundColor Yellow
foreach ($ref in $pathReferences) {
    if ($ref.ReferencedPath -match "Scripts\\.*dns" -or $ref.ReferencedPath -match "Scripts\\.*domain") {
        $newPath = $ref.ReferencedPath -replace "Scripts\\", "data\dns_reports\setup\"
        $pathUpdatesNeeded += [PSCustomObject]@{
            File = $ref.SourceFile
            OldPath = $ref.ReferencedPath
            NewPath = $newPath
        }
    }
}

$pathUpdatesNeeded | Format-Table File, OldPath, NewPath -AutoSize

Write-Host ""
Write-Host "Recommended Actions:" -ForegroundColor Cyan
Write-Host "1. Move DNS-related scripts from Scripts\ to data\dns_reports\setup\" -ForegroundColor White
Write-Host "2. Update path references in affected files" -ForegroundColor White
Write-Host "3. Test all DNS workflows after changes" -ForegroundColor White
Write-Host "4. Update documentation to reflect new file locations" -ForegroundColor White

Write-Host ""
Write-Host "Saving analysis results..." -ForegroundColor Yellow
$analysisResults = @{
    Timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss")
    DNSFiles = $dnsFiles
    PathReferences = $pathReferences
    ProposedMoves = $filesToMove
    PathUpdates = $pathUpdatesNeeded
}

$analysisResults | ConvertTo-Json -Depth 3 | Out-File "Scripts\dns_project_analysis.json" -Encoding UTF8
Write-Host "Analysis saved to: Scripts\dns_project_analysis.json" -ForegroundColor Green

Write-Host ""
Write-Host "Analysis complete!" -ForegroundColor Green
