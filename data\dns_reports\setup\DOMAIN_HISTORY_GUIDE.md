# DNS Domain History Tracking Guide

## 📊 Overview

The DNS Security Monitor now includes comprehensive domain history tracking that maintains a historical record of all domains accessed on your system. This feature provides valuable insights into browsing patterns, domain frequency, and security trends over time.

## 🗄️ File Structure

### Core Files
- **`dns_domain_history.csv`** - Main history database (flat file format)
- **`dns_domain_history_format.json`** - Complete format specification
- **`dns_domain_history_schema.sql`** - Optional SQL Server integration

### Data Format
Each domain record includes:
- **Domain Name** (primary key)
- **IP Address** (when available)
- **First Seen** / **Last Seen** timestamps
- **Occurrence Count** (increments each time seen)
- **Current Risk Level** / **Highest Risk Level Ever**
- **Threat Categories** (semicolon-separated)
- **DNS Record Type** (A, CNAME, etc.)
- **Created Date** / **Modified Date**

## 🔄 How It Works

### Automatic Updates
```
Windows Task Scheduler (every 60 minutes)
    ↓
Scripts\get_dns_cache.ps1 (collects DNS cache)
    ↓
Scripts\update_domain_history.ps1 (updates CSV)
    ↓
N8N Workflow (reads and analyzes with historical context)
```

### Update Logic
- **Existing Domains**: Row updated (occurrence count incremented, last_seen updated)
- **New Domains**: New row added with initial values
- **No Duplicates**: One row per domain (domain_name is unique key)

## 📋 Query Tools

### Basic Queries
```powershell
# Summary statistics
.\Scripts\query_domain_history.ps1 -Query summary

# Recent activity (last 7 days)
.\Scripts\query_domain_history.ps1 -Query recent -Days 7

# High-risk domains only
.\Scripts\query_domain_history.ps1 -Query highrisk

# Search for specific domain
.\Scripts\query_domain_history.ps1 -Query search -Domain "google"

# Export filtered data
.\Scripts\query_domain_history.ps1 -Query export -RiskLevel HIGH
```

### Sample Output
```
DNS Domain History Analysis
===========================
Total records: 25
  LOW: 22
  MEDIUM: 2
  HIGH: 1
Active in last 30 days: 18

TOP 5 MOST ACCESSED DOMAINS:
  google.com (45 times)
  github.com (32 times)
  microsoft.com (28 times)
  stackoverflow.com (15 times)
  docker.internal (12 times)
```

## 🎯 Use Cases

### Security Analysis
- **Trend Detection**: Identify new or suspicious domains
- **Frequency Analysis**: Monitor which domains are accessed most
- **Risk Evolution**: Track how domain risk levels change over time
- **Timeline Investigation**: Determine when suspicious activity started

### Business Intelligence
- **Usage Patterns**: Understand browsing and application behavior
- **Resource Planning**: Identify frequently accessed services
- **Compliance Monitoring**: Track access to specific domain categories

### System Administration
- **Network Troubleshooting**: Historical DNS resolution data
- **Performance Analysis**: Identify domains causing frequent lookups
- **Change Management**: Monitor impact of system changes on DNS patterns

## 🔧 Advanced Integration

### Excel Analysis
1. Open `dns_domain_history.csv` in Excel
2. Create pivot tables for domain frequency analysis
3. Generate charts showing domain access trends over time
4. Filter and sort by risk levels or date ranges

### SQL Server Integration (Optional)
1. Run `dns_domain_history_schema.sql` to create table
2. Use BULK INSERT to import CSV data
3. Run complex queries for advanced analytics
4. Create automated reports and dashboards

### Python/R Data Science
```python
import pandas as pd
df = pd.read_csv('dns_domain_history.csv')
# Analyze trends, create visualizations, build models
```

## 📈 Benefits

### Portability
- **No Database Required** - Works on any Windows system
- **Standard CSV Format** - Compatible with all analysis tools
- **Easy Backup** - Simple file copy for data preservation
- **Version Control Friendly** - Can track changes in Git

### Scalability
- **Efficient Updates** - Only modified rows are updated
- **Manageable Size** - Automatic cleanup options available
- **Fast Queries** - Indexed access patterns for common operations

### Integration
- **N8N Workflow** - Historical context in security reports
- **PowerShell Tools** - Built-in query and analysis capabilities
- **External Tools** - Easy import into any data analysis platform

## 🛠️ Maintenance

### File Management
- **Automatic Rotation**: Configure cleanup of old records
- **Backup Strategy**: Regular CSV file backups recommended
- **Size Monitoring**: Track file growth and performance

### Data Quality
- **Duplicate Prevention**: Domain names are unique keys
- **Timestamp Accuracy**: All times in ISO8601 UTC format
- **Risk Level Consistency**: Standardized LOW/MEDIUM/HIGH values

## 📞 Support

For questions about domain history tracking:
1. Review this guide and the format specification
2. Check PowerShell script logs for update status
3. Verify CSV file format and data integrity
4. Test query tools with sample data

The domain history system is designed for reliability and ease of use, providing valuable insights into your DNS activity patterns while maintaining maximum compatibility and portability.
