# Fix permissions for N8N Docker volume mount
Write-Host "Fixing N8N Docker volume permissions..." -ForegroundColor Cyan

Set-Location "n8n-docker"

# Fix ownership of the shared directory to node:node (UID 1000)
Write-Host "Setting ownership of shared directory to node:node..." -ForegroundColor Yellow
try {
    docker-compose exec n8n chown -R node:node /home/<USER>/shared
    Write-Host "✅ Ownership set to node:node" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Could not set ownership: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Set proper permissions (755 for directories, 644 for files)
Write-Host "Setting proper permissions..." -ForegroundColor Yellow
try {
    docker-compose exec n8n chmod -R 755 /home/<USER>/shared
    Write-Host "✅ Permissions set to 755" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Could not set permissions: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Test write permissions
Write-Host "Testing write permissions..." -ForegroundColor Yellow
try {
    docker-compose exec n8n touch /home/<USER>/shared/permission_test.txt
    if (Test-Path "../data/permission_test.txt") {
        Write-Host "✅ Write permissions working!" -ForegroundColor Green
        Remove-Item "../data/permission_test.txt" -Force
    } else {
        Write-Host "❌ Write permissions not working" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error testing permissions: $($_.Exception.Message)" -ForegroundColor Red
}

Set-Location ".."
Write-Host "Permission fix complete!" -ForegroundColor Green
