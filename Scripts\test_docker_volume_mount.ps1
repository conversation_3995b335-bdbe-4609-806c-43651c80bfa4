# Test Docker Volume Mount for N8N
Write-Host "Testing Docker Volume Mount..." -ForegroundColor Cyan

Set-Location "n8n-docker"

# Test 1: Create file from host side
Write-Host "`n1. Creating test file from host..." -ForegroundColor Yellow
$testContent = "Test from host at $(Get-Date)"
$testContent | Out-File -FilePath "../data/host_test.txt" -Encoding UTF8
Write-Host "Created: ../data/host_test.txt"

# Test 2: Check if container can see the file
Write-Host "`n2. Checking if container can see host file..." -ForegroundColor Yellow
try {
    $result = docker-compose exec n8n ls -la /home/<USER>/shared/host_test.txt
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Container can see host file" -ForegroundColor Green
    } else {
        Write-Host "❌ Container cannot see host file" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error checking container file access" -ForegroundColor Red
}

# Test 3: Create file from container side
Write-Host "`n3. Creating test file from container..." -ForegroundColor Yellow
try {
    docker-compose exec n8n sh -c "echo 'Test from container at $(date)' > /home/<USER>/shared/container_test.txt"
    if (Test-Path "../data/container_test.txt") {
        Write-Host "✅ Container file appears on host" -ForegroundColor Green
        $content = Get-Content "../data/container_test.txt"
        Write-Host "Content: $content"
    } else {
        Write-Host "❌ Container file does NOT appear on host" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error creating file from container" -ForegroundColor Red
}

# Test 4: Check container directory structure
Write-Host "`n4. Checking container directory structure..." -ForegroundColor Yellow
try {
    Write-Host "Contents of /home/<USER>"
    docker-compose exec n8n ls -la /home/<USER>/
    
    Write-Host "`nContents of /home/<USER>/shared (if exists):"
    docker-compose exec n8n ls -la /home/<USER>/shared/
} catch {
    Write-Host "Error checking container directories" -ForegroundColor Red
}

# Test 5: Check volume mounts
Write-Host "`n5. Checking Docker volume mounts..." -ForegroundColor Yellow
try {
    $containerInfo = docker-compose exec n8n mount | grep shared
    if ($containerInfo) {
        Write-Host "Volume mount info: $containerInfo" -ForegroundColor Green
    } else {
        Write-Host "No shared volume mount found" -ForegroundColor Red
    }
} catch {
    Write-Host "Error checking volume mounts" -ForegroundColor Red
}

# Cleanup test files
Write-Host "`n6. Cleaning up test files..." -ForegroundColor Yellow
Remove-Item "../data/host_test.txt" -ErrorAction SilentlyContinue
Remove-Item "../data/container_test.txt" -ErrorAction SilentlyContinue

Set-Location ".."
Write-Host "`nVolume mount test complete!" -ForegroundColor Green
