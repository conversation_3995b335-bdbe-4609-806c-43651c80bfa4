# Query DNS Domain History
# Simple PowerShell script to analyze domain history data

param(
    [string]$HistoryFile = "data\dns_reports\setup\dns_domain_history.csv",
    [string]$Query = "summary",
    [int]$Days = 30,
    [string]$Domain = "",
    [string]$RiskLevel = ""
)

if (-not (Test-Path $HistoryFile)) {
    Write-Host "ERROR: History file not found: $HistoryFile" -ForegroundColor Red
    exit 1
}

$history = Import-Csv $HistoryFile

Write-Host "DNS Domain History Analysis" -ForegroundColor Cyan
Write-Host "===========================" -ForegroundColor Cyan
Write-Host "History file: $HistoryFile" -ForegroundColor White
Write-Host "Total records: $($history.Count)" -ForegroundColor White
Write-Host ""

switch ($Query.ToLower()) {
    "summary" {
        Write-Host "SUMMARY STATISTICS:" -ForegroundColor Green
        Write-Host "Total domains: $($history.Count)" -ForegroundColor White
        
        $riskCounts = $history | Group-Object current_risk_level
        foreach ($risk in $riskCounts) {
            Write-Host "  $($risk.Name): $($risk.Count)" -ForegroundColor White
        }
        
        $recentDomains = $history | Where-Object { 
            [DateTime]::Parse($_.last_seen) -gt (Get-Date).AddDays(-$Days) 
        }
        Write-Host "Active in last $Days days: $($recentDomains.Count)" -ForegroundColor White
        
        $topDomains = $history | Sort-Object { [int]$_.occurrence_count } -Descending | Select-Object -First 5
        Write-Host "`nTOP 5 MOST ACCESSED DOMAINS:" -ForegroundColor Green
        foreach ($domain in $topDomains) {
            Write-Host "  $($domain.domain_name) ($($domain.occurrence_count) times)" -ForegroundColor White
        }
    }
    
    "recent" {
        Write-Host "RECENT DOMAINS (Last $Days days):" -ForegroundColor Green
        $recentDomains = $history | Where-Object { 
            [DateTime]::Parse($_.last_seen) -gt (Get-Date).AddDays(-$Days) 
        } | Sort-Object last_seen -Descending
        
        foreach ($domain in $recentDomains) {
            $lastSeen = [DateTime]::Parse($domain.last_seen).ToString("yyyy-MM-dd HH:mm")
            Write-Host "  $($domain.domain_name) - $lastSeen ($($domain.current_risk_level))" -ForegroundColor White
        }
    }
    
    "highrisk" {
        Write-Host "HIGH-RISK DOMAINS:" -ForegroundColor Red
        $highRisk = $history | Where-Object { $_.highest_risk_level -eq "HIGH" -or $_.current_risk_level -eq "HIGH" }
        
        foreach ($domain in $highRisk) {
            $lastSeen = [DateTime]::Parse($domain.last_seen).ToString("yyyy-MM-dd HH:mm")
            Write-Host "  $($domain.domain_name) - $lastSeen" -ForegroundColor Red
            Write-Host "    Current: $($domain.current_risk_level), Highest: $($domain.highest_risk_level)" -ForegroundColor Yellow
            if ($domain.threat_categories) {
                Write-Host "    Threats: $($domain.threat_categories)" -ForegroundColor Yellow
            }
        }
    }
    
    "search" {
        if (-not $Domain) {
            Write-Host "ERROR: -Domain parameter required for search query" -ForegroundColor Red
            exit 1
        }
        
        Write-Host "SEARCH RESULTS for '$Domain':" -ForegroundColor Green
        $matches = $history | Where-Object { $_.domain_name -like "*$Domain*" }
        
        foreach ($match in $matches) {
            $firstSeen = [DateTime]::Parse($match.first_seen).ToString("yyyy-MM-dd HH:mm")
            $lastSeen = [DateTime]::Parse($match.last_seen).ToString("yyyy-MM-dd HH:mm")
            Write-Host "  $($match.domain_name)" -ForegroundColor White
            Write-Host "    First seen: $firstSeen" -ForegroundColor Gray
            Write-Host "    Last seen: $lastSeen" -ForegroundColor Gray
            Write-Host "    Occurrences: $($match.occurrence_count)" -ForegroundColor Gray
            Write-Host "    Risk level: $($match.current_risk_level)" -ForegroundColor Gray
        }
    }
    
    "export" {
        $exportFile = "dns_domain_history_export_$(Get-Date -Format 'yyyyMMdd_HHmmss').csv"
        $filteredData = $history
        
        if ($RiskLevel) {
            $filteredData = $filteredData | Where-Object { $_.current_risk_level -eq $RiskLevel }
        }
        
        if ($Days -lt 365) {
            $filteredData = $filteredData | Where-Object { 
                [DateTime]::Parse($_.last_seen) -gt (Get-Date).AddDays(-$Days) 
            }
        }
        
        $filteredData | Export-Csv -Path $exportFile -NoTypeInformation
        Write-Host "Exported $($filteredData.Count) records to: $exportFile" -ForegroundColor Green
    }
    
    default {
        Write-Host "Available queries:" -ForegroundColor Yellow
        Write-Host "  summary    - Overall statistics" -ForegroundColor White
        Write-Host "  recent     - Domains seen recently (-Days parameter)" -ForegroundColor White
        Write-Host "  highrisk   - High-risk domains only" -ForegroundColor White
        Write-Host "  search     - Search for specific domain (-Domain parameter)" -ForegroundColor White
        Write-Host "  export     - Export filtered data to CSV" -ForegroundColor White
        Write-Host ""
        Write-Host "Examples:" -ForegroundColor Yellow
        Write-Host "  .\Scripts\query_domain_history.ps1 -Query summary" -ForegroundColor Gray
        Write-Host "  .\Scripts\query_domain_history.ps1 -Query recent -Days 7" -ForegroundColor Gray
        Write-Host "  .\Scripts\query_domain_history.ps1 -Query search -Domain 'google'" -ForegroundColor Gray
        Write-Host "  .\Scripts\query_domain_history.ps1 -Query export -RiskLevel HIGH" -ForegroundColor Gray
    }
}
