# Centralize DNS Security Project Files
# This script moves all DNS-related files to the setup folder and updates path references

param(
    [switch]$DryRun,
    [switch]$Verbose
)

Write-Host "DNS Security Project Centralization" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan
Write-Host ""

if ($DryRun) {
    Write-Host "DRY RUN MODE - No files will be moved or modified" -ForegroundColor Yellow
    Write-Host ""
}

# Define files to move from Scripts to setup folder
$filesToMove = @(
    @{ Source = "Scripts\create_dns_reports_directory.ps1"; Target = "data\dns_reports\setup\create_dns_reports_directory.ps1" },
    @{ Source = "Scripts\dns_cache_loop.ps1"; Target = "data\dns_reports\setup\dns_cache_loop.ps1" },
    @{ Source = "Scripts\save_dns_report.ps1"; Target = "data\dns_reports\setup\save_dns_report.ps1" },
    @{ Source = "Scripts\setup_dns_automation.ps1"; Target = "data\dns_reports\setup\setup_dns_automation.ps1" },
    @{ Source = "Scripts\setup_dns_monitoring.ps1"; Target = "data\dns_reports\setup\setup_dns_monitoring.ps1" },
    @{ Source = "Scripts\test_dns_workflow.py"; Target = "data\dns_reports\setup\test_dns_workflow.py" },
    @{ Source = "Scripts\query_domain_history.ps1"; Target = "data\dns_reports\setup\query_domain_history.ps1" },
    @{ Source = "Scripts\update_domain_history.ps1"; Target = "data\dns_reports\setup\update_domain_history.ps1" }
)

# Define path updates needed in files
$pathUpdates = @(
    # Files that reference Scripts\get_dns_cache.ps1 (already moved)
    @{ File = "data\dns_reports\setup\collect_dns_cache.bat"; OldPath = "Scripts\\get_dns_cache.ps1"; NewPath = "data\\dns_reports\\setup\\get_dns_cache.ps1" },
    @{ File = "data\dns_reports\setup\collect_dns_cache.ps1"; OldPath = "Scripts\\get_dns_cache.ps1"; NewPath = "data\\dns_reports\\setup\\get_dns_cache.ps1" },
    
    # Files that reference Scripts\update_domain_history.ps1 (will be moved)
    @{ File = "data\dns_reports\setup\get_dns_cache.ps1"; OldPath = "Scripts\\update_domain_history.ps1"; NewPath = "data\\dns_reports\\setup\\update_domain_history.ps1" },
    
    # Files that reference Scripts\setup_dns_automation.ps1 (will be moved)
    @{ File = "Scripts\setup_dns_monitoring.ps1"; OldPath = "Scripts\\setup_dns_automation.ps1"; NewPath = "data\\dns_reports\\setup\\setup_dns_automation.ps1" },
    @{ File = "Scripts\setup_dns_monitoring.ps1"; OldPath = "Scripts\\\\setup_dns_automation.ps1"; NewPath = "data\\dns_reports\\setup\\setup_dns_automation.ps1" },
    
    # Files that reference Scripts\dns_cache_loop.ps1 (will be moved)
    @{ File = "Scripts\setup_dns_monitoring.ps1"; OldPath = "Scripts\\dns_cache_loop.ps1"; NewPath = "data\\dns_reports\\setup\\dns_cache_loop.ps1" },
    
    # Files that reference Scripts\get_dns_cache.ps1 (already moved)
    @{ File = "Scripts\setup_dns_monitoring.ps1"; OldPath = "Scripts\\get_dns_cache.ps1"; NewPath = "data\\dns_reports\\setup\\get_dns_cache.ps1" },
    @{ File = "Scripts\setup_dns_monitoring.ps1"; OldPath = ".\\Scripts\\get_dns_cache.ps1"; NewPath = ".\\data\\dns_reports\\setup\\get_dns_cache.ps1" }
)

Write-Host "Step 1: Moving DNS-related files to setup folder" -ForegroundColor Green
Write-Host ""

foreach ($move in $filesToMove) {
    $sourcePath = $move.Source
    $targetPath = $move.Target
    
    if (Test-Path $sourcePath) {
        Write-Host "Moving: $sourcePath -> $targetPath" -ForegroundColor White
        
        if (-not $DryRun) {
            try {
                # Ensure target directory exists
                $targetDir = Split-Path $targetPath -Parent
                if (-not (Test-Path $targetDir)) {
                    New-Item -Path $targetDir -ItemType Directory -Force | Out-Null
                }
                
                # Move the file
                Move-Item -Path $sourcePath -Destination $targetPath -Force
                Write-Host "  SUCCESS: File moved" -ForegroundColor Green
            } catch {
                Write-Host "  ERROR: Failed to move file: $($_.Exception.Message)" -ForegroundColor Red
            }
        } else {
            Write-Host "  [DRY RUN] Would move file" -ForegroundColor Gray
        }
    } else {
        Write-Host "  WARNING: Source file not found: $sourcePath" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Step 2: Updating path references in files" -ForegroundColor Green
Write-Host ""

foreach ($update in $pathUpdates) {
    $filePath = $update.File
    $oldPath = $update.OldPath
    $newPath = $update.NewPath
    
    if (Test-Path $filePath) {
        Write-Host "Updating paths in: $filePath" -ForegroundColor White
        Write-Host "  $oldPath -> $newPath" -ForegroundColor Gray
        
        if (-not $DryRun) {
            try {
                $content = Get-Content $filePath -Raw -Encoding UTF8
                if ($content -and $content.Contains($oldPath)) {
                    $updatedContent = $content -replace [regex]::Escape($oldPath), $newPath
                    $updatedContent | Out-File -FilePath $filePath -Encoding UTF8 -NoNewline
                    Write-Host "  SUCCESS: Path updated" -ForegroundColor Green
                } else {
                    Write-Host "  INFO: Path not found in file" -ForegroundColor Gray
                }
            } catch {
                Write-Host "  ERROR: Failed to update file: $($_.Exception.Message)" -ForegroundColor Red
            }
        } else {
            Write-Host "  [DRY RUN] Would update path" -ForegroundColor Gray
        }
    } else {
        Write-Host "  WARNING: File not found: $filePath" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Step 3: Updating documentation references" -ForegroundColor Green
Write-Host ""

# Update documentation files that reference the moved scripts
$docFiles = @(
    "Documentation\DNS_Security_Monitor_README.md",
    "data\dns_reports\setup\README.md"
)

foreach ($docFile in $docFiles) {
    if (Test-Path $docFile) {
        Write-Host "Updating documentation: $docFile" -ForegroundColor White
        
        if (-not $DryRun) {
            try {
                $content = Get-Content $docFile -Raw -Encoding UTF8
                $updated = $false
                
                # Update references to moved scripts
                foreach ($move in $filesToMove) {
                    $scriptName = Split-Path $move.Source -Leaf
                    $oldRef = "Scripts\$scriptName"
                    $newRef = "data\dns_reports\setup\$scriptName"
                    
                    if ($content.Contains($oldRef)) {
                        $content = $content -replace [regex]::Escape($oldRef), $newRef
                        $updated = $true
                    }
                }
                
                if ($updated) {
                    $content | Out-File -FilePath $docFile -Encoding UTF8 -NoNewline
                    Write-Host "  SUCCESS: Documentation updated" -ForegroundColor Green
                } else {
                    Write-Host "  INFO: No updates needed" -ForegroundColor Gray
                }
            } catch {
                Write-Host "  ERROR: Failed to update documentation: $($_.Exception.Message)" -ForegroundColor Red
            }
        } else {
            Write-Host "  [DRY RUN] Would update documentation" -ForegroundColor Gray
        }
    }
}

Write-Host ""
Write-Host "Step 4: Creating centralized launcher script" -ForegroundColor Green
Write-Host ""

$launcherPath = "data\dns_reports\setup\run_dns_setup.ps1"
$launcherContent = @"
# DNS Security Monitor - Centralized Setup Launcher
# All DNS-related scripts are now located in this setup folder

Write-Host "DNS Security Monitor - Setup Options" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Available setup scripts:" -ForegroundColor White
Write-Host "1. setup_dns_monitoring.ps1    - Main setup script" -ForegroundColor Green
Write-Host "2. setup_dns_automation.ps1    - Task scheduler setup" -ForegroundColor Green
Write-Host "3. get_dns_cache.ps1           - Manual DNS collection" -ForegroundColor Green
Write-Host "4. dns_cache_loop.ps1          - Continuous collection" -ForegroundColor Green
Write-Host "5. query_domain_history.ps1    - Query domain history" -ForegroundColor Green
Write-Host ""
Write-Host "Quick start: Run setup_dns_monitoring.ps1 for automated setup" -ForegroundColor Yellow
Write-Host ""

# Change to the setup directory for relative paths to work
Set-Location (Split-Path -Parent $MyInvocation.MyCommand.Path)
"@

if (-not $DryRun) {
    $launcherContent | Out-File -FilePath $launcherPath -Encoding UTF8
    Write-Host "Created centralized launcher: $launcherPath" -ForegroundColor Green
} else {
    Write-Host "[DRY RUN] Would create launcher: $launcherPath" -ForegroundColor Gray
}

Write-Host ""
Write-Host "Centralization Summary:" -ForegroundColor Cyan
Write-Host "======================" -ForegroundColor Cyan
Write-Host "Files moved: $($filesToMove.Count)" -ForegroundColor White
Write-Host "Path updates: $($pathUpdates.Count)" -ForegroundColor White
Write-Host "Documentation files updated: $($docFiles.Count)" -ForegroundColor White
Write-Host ""

if ($DryRun) {
    Write-Host "This was a DRY RUN - no changes were made" -ForegroundColor Yellow
    Write-Host "Run without -DryRun to perform the actual migration" -ForegroundColor Yellow
} else {
    Write-Host "DNS Security Project centralization complete!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Test DNS workflows to ensure everything works" -ForegroundColor White
    Write-Host "2. Update any Windows Task Scheduler tasks if needed" -ForegroundColor White
    Write-Host "3. Use the new centralized launcher: data\dns_reports\setup\run_dns_setup.ps1" -ForegroundColor White
}

Write-Host ""
