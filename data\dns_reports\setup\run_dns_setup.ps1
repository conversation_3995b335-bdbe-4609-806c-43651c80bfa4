﻿# DNS Security Monitor - Centralized Setup Launcher
# All DNS-related scripts are now located in this setup folder

Write-Host "DNS Security Monitor - Setup Options" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Available setup scripts:" -ForegroundColor White
Write-Host "1. setup_dns_monitoring.ps1    - Main setup script" -ForegroundColor Green
Write-Host "2. setup_dns_automation.ps1    - Task scheduler setup" -ForegroundColor Green
Write-Host "3. get_dns_cache.ps1           - Manual DNS collection" -ForegroundColor Green
Write-Host "4. dns_cache_loop.ps1          - Continuous collection" -ForegroundColor Green
Write-Host "5. query_domain_history.ps1    - Query domain history" -ForegroundColor Green
Write-Host ""
Write-Host "Quick start: Run setup_dns_monitoring.ps1 for automated setup" -ForegroundColor Yellow
Write-Host ""

# Change to the setup directory for relative paths to work
Set-Location (Split-Path -Parent $MyInvocation.MyCommand.Path)
