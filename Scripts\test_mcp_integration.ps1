# Test N8N MCP Integration
# This script tests the complete MCP integration with VS Code

Write-Host "Testing N8N MCP Integration" -ForegroundColor Cyan
Write-Host "=============================" -ForegroundColor Cyan

# Test 1: Check VS Code version
Write-Host "`nTest 1: VS Code Version Check" -ForegroundColor Yellow
try {
    # Try to get VS Code version from environment (if running in VS Code terminal)
    $vscodeVersion = $env:TERM_PROGRAM_VERSION
    if ($vscodeVersion) {
        Write-Host "VS Code version: $vscodeVersion" -ForegroundColor Green
        
        # Parse version
        if ($vscodeVersion -match "^(\d+)\.(\d+)") {
            $major = [int]$matches[1]
            $minor = [int]$matches[2]
            
            if ($major -eq 1 -and $minor -ge 102) {
                Write-Host "✅ VS Code supports MCP" -ForegroundColor Green
            } else {
                Write-Host "❌ VS Code version too old for MCP" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "⚠️  Not running in VS Code terminal" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Could not detect VS Code version" -ForegroundColor Yellow
}

# Test 2: Check MCP package
Write-Host "`nTest 2: MCP Package Check" -ForegroundColor Yellow
try {
    python -c "import mcp; print('MCP package available')" 2>$null | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ MCP package installed" -ForegroundColor Green
    } else {
        Write-Host "❌ MCP package not found" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Python or MCP package issue" -ForegroundColor Red
}

# Test 3: Check MCP configuration
Write-Host "`nTest 3: MCP Configuration Check" -ForegroundColor Yellow
$mcpConfigPath = ".vscode\mcp.json"
if (Test-Path $mcpConfigPath) {
    Write-Host "✅ MCP configuration found" -ForegroundColor Green
    
    try {
        $mcpConfig = Get-Content $mcpConfigPath | ConvertFrom-Json
        $serverCount = $mcpConfig.servers.PSObject.Properties.Count
        Write-Host "   Configured servers: $serverCount" -ForegroundColor White
        
        foreach ($serverName in $mcpConfig.servers.PSObject.Properties.Name) {
            Write-Host "   - $serverName" -ForegroundColor Gray
        }
    } catch {
        Write-Host "⚠️  Could not parse MCP configuration" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ MCP configuration not found" -ForegroundColor Red
}

# Test 4: Test MCP servers
Write-Host "`nTest 4: MCP Server Tests" -ForegroundColor Yellow

$servers = @(
    @{Name="Research Server"; Module="n8n_builder.mcp_research_server"},
    @{Name="Database Server"; Module="n8n_builder.mcp_database_server"},
    @{Name="Workflow Server"; Module="n8n_builder.mcp_workflow_server"}
)

$allServersWorking = $true

foreach ($server in $servers) {
    Write-Host "Testing $($server.Name)..." -ForegroundColor Gray
    
    try {
        $testResult = python -c "
import sys
sys.path.insert(0, '.')
try:
    from $($server.Module) import server
    print('OK')
except Exception as e:
    print(f'ERROR: {e}')
" 2>&1
        
        if ($testResult -match "OK") {
            Write-Host "   ✅ $($server.Name)" -ForegroundColor Green
        } else {
            Write-Host "   ❌ $($server.Name): $testResult" -ForegroundColor Red
            $allServersWorking = $false
        }
    } catch {
        Write-Host "   ❌ $($server.Name): Failed to test" -ForegroundColor Red
        $allServersWorking = $false
    }
}

# Test 5: Check dependencies
Write-Host "`nTest 5: Dependency Check" -ForegroundColor Yellow

$dependencies = @("mcp", "httpx", "jsonschema", "pydantic")
$allDepsOk = $true

foreach ($dep in $dependencies) {
    try {
        python -c "import $dep; print('OK')" 2>$null | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✅ $dep" -ForegroundColor Green
        } else {
            Write-Host "   ❌ $dep" -ForegroundColor Red
            $allDepsOk = $false
        }
    } catch {
        Write-Host "   ❌ $dep" -ForegroundColor Red
        $allDepsOk = $false
    }
}

# Test 6: Check N8N_Builder components
Write-Host "`nTest 6: N8N_Builder Component Check" -ForegroundColor Yellow

$components = @(
    "n8n_builder.config",
    "n8n_builder.mcp_research_tool",
    "n8n_builder.mcp_database_tool",
    "n8n_builder.n8n_builder"
)

$allComponentsOk = $true

foreach ($component in $components) {
    try {
        python -c "import sys; sys.path.insert(0, '.'); import $component; print('OK')" 2>$null | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✅ $component" -ForegroundColor Green
        } else {
            Write-Host "   ❌ $component" -ForegroundColor Red
            $allComponentsOk = $false
        }
    } catch {
        Write-Host "   ❌ $component" -ForegroundColor Red
        $allComponentsOk = $false
    }
}

# Final Results
Write-Host "`nTest Results Summary" -ForegroundColor Cyan
Write-Host "====================" -ForegroundColor Cyan

if ($allServersWorking -and $allDepsOk -and $allComponentsOk) {
    Write-Host "🎉 ALL TESTS PASSED!" -ForegroundColor Green
    Write-Host "`nYour N8N MCP integration is ready to use!" -ForegroundColor Green
    
    Write-Host "`nNext Steps:" -ForegroundColor White
    Write-Host "1. Open VS Code" -ForegroundColor Gray
    Write-Host "2. Start a chat session (Ctrl+Alt+I)" -ForegroundColor Gray
    Write-Host "3. Enable Agent mode" -ForegroundColor Gray
    Write-Host "4. Click Tools to see N8N_Builder tools" -ForegroundColor Gray
    Write-Host "5. Try: 'Search for HTTP Request node documentation'" -ForegroundColor Gray
    
    Write-Host "`nDocumentation:" -ForegroundColor White
    Write-Host "- Full guide: Documentation\MCP_VS_Code_Integration_Guide.md" -ForegroundColor Gray
    
} else {
    Write-Host "❌ SOME TESTS FAILED" -ForegroundColor Red
    Write-Host "`nIssues detected:" -ForegroundColor Yellow
    
    if (-not $allServersWorking) {
        Write-Host "- MCP servers have issues" -ForegroundColor Red
    }
    if (-not $allDepsOk) {
        Write-Host "- Missing dependencies" -ForegroundColor Red
    }
    if (-not $allComponentsOk) {
        Write-Host "- N8N_Builder components have issues" -ForegroundColor Red
    }
    
    Write-Host "`nTroubleshooting:" -ForegroundColor White
    Write-Host "1. Check Python virtual environment is active" -ForegroundColor Gray
    Write-Host "2. Run: pip install -r requirements_mcp.txt" -ForegroundColor Gray
    Write-Host "3. Check logs in logs/ folder" -ForegroundColor Gray
    Write-Host "4. Restart VS Code" -ForegroundColor Gray
}

Write-Host "`nTest completed!" -ForegroundColor Cyan
