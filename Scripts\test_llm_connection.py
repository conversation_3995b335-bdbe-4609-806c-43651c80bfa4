#!/usr/bin/env python3
"""
Test LLM Connection
"""

import asyncio
import sys
import os
import json
from pathlib import Path

# Add the parent directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

import httpx

async def test_llm_direct():
    """Test LLM connection directly."""
    print("🤖 Testing LLM Connection")
    print("=" * 30)
    
    # Set the model environment variable
    os.environ["MIMO_MODEL"] = "deepseek-r1-distill-llama-8b"
    
    endpoint = "http://localhost:1234/v1/chat/completions"
    
    # Test payload
    payload = {
        "model": "deepseek-r1-distill-llama-8b",
        "messages": [
            {
                "role": "user", 
                "content": "Create a simple JSON object with 'message': 'Hello from N8N Builder test'"
            }
        ],
        "temperature": 0.7,
        "max_tokens": 100
    }
    
    try:
        async with httpx.AsyncClient(timeout=30) as client:
            print(f"Sending request to: {endpoint}")
            print(f"Model: {payload['model']}")
            
            response = await client.post(endpoint, json=payload)
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ LLM connection successful!")
                
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    print(f"Response: {content}")
                    return True
                else:
                    print("⚠️  Unexpected response format")
                    print(f"Response: {result}")
                    return False
            else:
                print(f"❌ LLM request failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ LLM connection error: {e}")
        return False

async def test_n8n_builder_simple():
    """Test N8N Builder with simple request."""
    print("\n🔧 Testing N8N Builder Simple")
    print("=" * 30)
    
    try:
        from n8n_builder.n8n_builder import N8NBuilder
        
        builder = N8NBuilder()
        print("✅ N8N Builder initialized")
        
        # Very simple test
        simple_description = "Create a workflow with a manual trigger and a webhook node"
        
        print(f"Testing with: {simple_description}")
        
        result = builder.generate_workflow(simple_description)
        
        if result:
            print(f"✅ Got result (length: {len(result)})")
            print(f"First 200 chars: {result[:200]}...")
            return True
        else:
            print("❌ No result returned")
            return False
            
    except Exception as e:
        print(f"❌ N8N Builder test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run tests."""
    print("🚀 LLM and N8N Builder Connection Tests")
    print("=" * 50)
    
    # Test direct LLM connection
    llm_success = await test_llm_direct()
    
    # Test N8N Builder if LLM works
    if llm_success:
        builder_success = await test_n8n_builder_simple()
    else:
        print("\n⚠️  Skipping N8N Builder test due to LLM connection failure")
        builder_success = False
    
    print(f"\n📊 Results:")
    print(f"LLM Connection: {'✅ PASS' if llm_success else '❌ FAIL'}")
    print(f"N8N Builder: {'✅ PASS' if builder_success else '❌ FAIL'}")

if __name__ == "__main__":
    asyncio.run(main())
