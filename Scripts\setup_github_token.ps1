# Setup GitHub API Token for N8N MCP Research
# This script helps configure a GitHub API token for better documentation access

Write-Host "🔧 GitHub API Token Setup for N8N MCP Research" -ForegroundColor Cyan
Write-Host "=" * 50

# Check if token already exists
$existingToken = $env:GITHUB_API_TOKEN
if ($existingToken) {
    Write-Host "✅ GitHub API token already configured (length: $($existingToken.Length))" -ForegroundColor Green
    Write-Host "Current token starts with: $($existingToken.Substring(0, [Math]::Min(10, $existingToken.Length)))..." -ForegroundColor Yellow
    
    $response = Read-Host "Do you want to update the token? (y/N)"
    if ($response -ne 'y' -and $response -ne 'Y') {
        Write-Host "Keeping existing token." -ForegroundColor Green
        exit 0
    }
}

Write-Host ""
Write-Host "📋 To create a GitHub API token:" -ForegroundColor Yellow
Write-Host "1. Go to https://github.com/settings/tokens" -ForegroundColor White
Write-Host "2. Click 'Generate new token' > 'Generate new token (classic)'" -ForegroundColor White
Write-Host "3. Give it a name like 'N8N MCP Research'" -ForegroundColor White
Write-Host "4. Select these scopes:" -ForegroundColor White
Write-Host "   - public_repo (for accessing public repositories)" -ForegroundColor White
Write-Host "   - read:org (optional, for organization repositories)" -ForegroundColor White
Write-Host "5. Click 'Generate token' and copy the token" -ForegroundColor White
Write-Host ""

# Prompt for token
$token = Read-Host "Enter your GitHub API token (or press Enter to skip)"

if ([string]::IsNullOrWhiteSpace($token)) {
    Write-Host "⚠️  No token provided. MCP research will use unauthenticated requests (rate limited)." -ForegroundColor Yellow
    exit 0
}

# Validate token format (should start with ghp_ for personal access tokens)
if (-not $token.StartsWith("ghp_") -and -not $token.StartsWith("github_pat_")) {
    Write-Host "⚠️  Warning: Token doesn't match expected format (should start with 'ghp_' or 'github_pat_')" -ForegroundColor Yellow
    $continue = Read-Host "Continue anyway? (y/N)"
    if ($continue -ne 'y' -and $continue -ne 'Y') {
        Write-Host "Setup cancelled." -ForegroundColor Red
        exit 1
    }
}

# Set environment variable for current session
$env:GITHUB_API_TOKEN = $token
Write-Host "✅ GitHub API token set for current session." -ForegroundColor Green

# Offer to set permanently
Write-Host ""
$setPermanent = Read-Host "Set token permanently for this user? (y/N)"
if ($setPermanent -eq 'y' -or $setPermanent -eq 'Y') {
    try {
        [Environment]::SetEnvironmentVariable("GITHUB_API_TOKEN", $token, "User")
        Write-Host "✅ GitHub API token set permanently for user." -ForegroundColor Green
        Write-Host "   You may need to restart your terminal/IDE for it to take effect." -ForegroundColor Yellow
    }
    catch {
        Write-Host "❌ Failed to set permanent environment variable: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "   Token is still set for current session." -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "🧪 Testing token..." -ForegroundColor Cyan

# Test the token by making a simple API call
try {
    $headers = @{
        "Authorization" = "token $token"
        "Accept" = "application/vnd.github.v3+json"
        "User-Agent" = "N8N-MCP-Research/1.0"
    }
    
    $response = Invoke-RestMethod -Uri "https://api.github.com/rate_limit" -Headers $headers -Method Get
    
    Write-Host "✅ Token is valid!" -ForegroundColor Green
    Write-Host "   Rate limit: $($response.rate.limit) requests/hour" -ForegroundColor Green
    Write-Host "   Remaining: $($response.rate.remaining) requests" -ForegroundColor Green
    Write-Host "   Reset time: $(Get-Date -UnixTimeSeconds $response.rate.reset)" -ForegroundColor Green
}
catch {
    Write-Host "❌ Token validation failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Please check your token and try again." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🚀 Setup complete! You can now run the MCP research tests with:" -ForegroundColor Cyan
Write-Host "   python Scripts/test_mcp_research.py" -ForegroundColor White
