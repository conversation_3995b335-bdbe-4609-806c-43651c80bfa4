# Restart N8N with shared volume for DNS reports
# This script restarts the N8N Docker container with the new shared volume mount

Write-Host "🔄 Restarting N8N with Shared Volume for DNS Reports" -ForegroundColor Cyan
Write-Host "=" * 60

# Change to N8N Docker directory
$N8NDir = "n8n-docker"
if (-not (Test-Path $N8NDir)) {
    Write-Host "❌ N8N Docker directory not found: $N8NDir" -ForegroundColor Red
    Write-Host "Please run this script from the N8N_Builder root directory" -ForegroundColor Yellow
    exit 1
}

Set-Location $N8NDir

Write-Host "📂 Current directory: $(Get-Location)" -ForegroundColor Green

# Check if docker-compose.yml exists
if (-not (Test-Path "docker-compose.yml")) {
    Write-Host "❌ docker-compose.yml not found in $N8NDir" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Found docker-compose.yml" -ForegroundColor Green

# Show the new volume mount
Write-Host "`n📋 New Volume Mount Added:" -ForegroundColor Yellow
Write-Host "   Host: ../data" -ForegroundColor White
Write-Host "   Container: /home/<USER>/shared" -ForegroundColor White
Write-Host "   Purpose: DNS reports and alerts storage" -ForegroundColor White

# Stop current N8N container
Write-Host "`n🛑 Stopping N8N container..." -ForegroundColor Yellow
try {
    docker-compose down
    Write-Host "✅ N8N container stopped" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Warning: Error stopping container: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Start N8N with new configuration
Write-Host "`n🚀 Starting N8N with new volume mount..." -ForegroundColor Yellow
try {
    docker-compose up -d
    Write-Host "✅ N8N container started with shared volume" -ForegroundColor Green
} catch {
    Write-Host "❌ Error starting N8N: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Wait a moment for container to start
Write-Host "`n⏳ Waiting for N8N to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check container status
Write-Host "`n📊 Container Status:" -ForegroundColor Cyan
docker-compose ps

# Verify volume mount
Write-Host "`n🔍 Verifying volume mount..." -ForegroundColor Yellow
try {
    $volumeCheck = docker-compose exec n8n ls -la /home/<USER>/shared
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Shared volume mounted successfully!" -ForegroundColor Green
        Write-Host "   Container path: /home/<USER>/shared" -ForegroundColor White
        Write-Host "   Host path: ../data" -ForegroundColor White
    } else {
        Write-Host "⚠️  Could not verify volume mount - container may still be starting" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  Could not verify volume mount: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Return to original directory
Set-Location ..

Write-Host "`n🎉 N8N Restart Complete!" -ForegroundColor Green
Write-Host "`n📋 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Open N8N: http://localhost:5678" -ForegroundColor White
Write-Host "2. Import the updated DNS Security workflow" -ForegroundColor White
Write-Host "3. Run the workflow to test file saving" -ForegroundColor White
Write-Host "4. Check ../data/ directory for generated reports" -ForegroundColor White
Write-Host "`n🌐 N8N should now be able to save files to: ../data/" -ForegroundColor Green
