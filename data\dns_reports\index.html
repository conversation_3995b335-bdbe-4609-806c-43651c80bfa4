<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DNS Security Reports Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.8;
            font-size: 1.1em;
        }
        
        .content {
            padding: 40px;
        }
        
        .status-card {
            background: #f8f9fa;
            border-left: 5px solid #28a745;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 5px;
        }
        
        .status-card.alert {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        
        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .report-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .report-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .report-card h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 1.3em;
        }
        
        .report-card p {
            color: #6c757d;
            margin: 5px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 15px;
            transition: background 0.2s;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn.alert {
            background: #dc3545;
        }
        
        .btn.alert:hover {
            background: #c82333;
        }
        
        .instructions {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .instructions h3 {
            margin: 0 0 15px 0;
            color: #1976d2;
        }
        
        .refresh-info {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .timestamp {
            font-size: 0.9em;
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ DNS Security Reports Dashboard</h1>
            <p>Automated DNS Cache Security Monitoring</p>
        </div>
        
        <div class="content">
            <div class="status-card" id="statusCard">
                <h3>📊 System Status</h3>
                <p><strong>Last Scan:</strong> <span id="lastScan">Waiting for first scan...</span></p>
                <p><strong>Reports Generated:</strong> <span id="reportCount">0</span></p>
                <p><strong>Alerts:</strong> <span id="alertCount">0</span></p>
            </div>
            
            <div class="reports-grid" id="reportsGrid">
                <div class="report-card">
                    <h3>📄 Latest DNS Security Report</h3>
                    <p><strong>Status:</strong> Ready for viewing</p>
                    <p><strong>Location:</strong> ./data/dns_reports/</p>
                    <p>Reports are automatically generated every 60 minutes by the N8N workflow.</p>
                    <a href="dns_security_report_2025-08-16.html" class="btn" target="_blank">View Latest Report</a>
                    <button onclick="openReportsFolder()" class="btn" style="margin-left: 10px; background: #6c757d;">📁 Open Folder</button>
                </div>

                <div class="report-card">
                    <h3>🚨 Security Alerts</h3>
                    <p><strong>Status:</strong> Monitoring active</p>
                    <p><strong>Location:</strong> ./data/dns_reports/</p>
                    <p>High-risk domain alerts are saved here when detected.</p>
                    <button onclick="listAlertFiles()" class="btn alert">📋 List Alert Files</button>
                    <button onclick="openReportsFolder()" class="btn" style="margin-left: 10px; background: #6c757d;">📁 Open Folder</button>
                </div>

                <div class="report-card">
                    <h3>⚙️ N8N Workflow</h3>
                    <p><strong>Status:</strong> Running every 60 minutes</p>
                    <p><strong>Access:</strong> http://localhost:5678</p>
                    <p>Monitor and manage the DNS security workflow.</p>
                    <a href="http://localhost:5678" target="_blank" class="btn">Open N8N</a>
                </div>

                <div class="report-card" id="recentReports">
                    <h3>📊 Recent Reports</h3>
                    <p><strong>Status:</strong> Auto-refreshing</p>
                    <div id="reportsList">
                        <p>Loading recent reports...</p>
                    </div>
                </div>
            </div>
            
            <div class="instructions">
                <h3>📋 How to Use This Dashboard</h3>
                <ul>
                    <li><strong>Automatic Updates:</strong> This page refreshes every 60 seconds to show new reports</li>
                    <li><strong>View Reports:</strong> Click on any report card to open the detailed security analysis</li>
                    <li><strong>Alert Files:</strong> High-risk domain alerts are saved separately for immediate attention</li>
                    <li><strong>File Location:</strong> All reports are saved in <code>./data/dns_reports/</code></li>
                    <li><strong>Workflow Schedule:</strong> DNS scans run every 60 minutes automatically</li>
                </ul>
            </div>
            
            <div class="refresh-info">
                <p class="timestamp">Page last updated: <span id="lastUpdate"></span></p>
                <button onclick="location.reload()" class="btn">🔄 Refresh Now</button>
            </div>
        </div>
    </div>

    <script>
        // Update timestamp
        document.getElementById('lastUpdate').textContent = new Date().toLocaleString();
        
        // Auto-refresh every 60 seconds
        setInterval(() => {
            location.reload();
        }, 60000);
        
        // Function to open the reports folder in Windows Explorer
        function openReportsFolder() {
            // This will work if the page is opened as a file:// URL
            try {
                // Try to open the current directory
                window.open('.', '_blank');
            } catch (e) {
                alert('To open the reports folder:\n\n1. Navigate to: C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\dns_reports\\\n2. Or use Windows Explorer to browse to the project data/dns_reports folder');
            }
        }

        // Function to list alert files
        function listAlertFiles() {
            // Since we can't directly read the file system from a web page,
            // we'll provide instructions for finding alert files
            const alertInfo = `
Alert Files Information:

📁 Location: ./data/dns_reports/
🔍 File Pattern: DNS_SECURITY_ALERT_YYYY-MM-DD_timestamp.txt

Recent Alert Files:
• Look for files starting with "DNS_SECURITY_ALERT_"
• Files are created only when high-risk domains are detected
• Each file contains detailed threat information

To view alert files:
1. Open the dns_reports folder
2. Look for .txt files with "ALERT" in the name
3. Double-click to open in text editor
            `;
            alert(alertInfo);
        }

        // Function to scan for recent reports (simulated)
        function loadRecentReports() {
            const reportsList = document.getElementById('reportsList');

            // Since we can't actually scan the directory from a web page,
            // we'll show the known report file
            const reportsHTML = `
                <div style="margin: 10px 0;">
                    <strong>📄 dns_security_report_2025-08-16.html</strong><br>
                    <small>Latest DNS security analysis</small><br>
                    <a href="dns_security_report_2025-08-16.html" target="_blank" style="color: #007bff; text-decoration: none;">📖 Open Report</a>
                </div>
                <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                    <small><strong>Note:</strong> This dashboard shows the most recent report.
                    For a complete list of all reports and alerts, use the "📁 Open Folder" button above.</small>
                </div>
            `;

            reportsList.innerHTML = reportsHTML;
        }

        // Load recent reports on page load
        loadRecentReports();
    </script>
</body>
</html>
