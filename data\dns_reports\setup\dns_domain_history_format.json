{"format_version": "1.0", "description": "DNS Domain History Tracking Format", "created_date": "2025-08-16", "file_encoding": "UTF-8", "delimiter": ",", "quote_character": "\"", "escape_character": "\\", "header_row": true, "columns": [{"name": "domain_name", "type": "string", "max_length": 255, "description": "Fully qualified domain name", "example": "telemetry.n8n.io", "required": true, "primary_key": true}, {"name": "ip_address", "type": "string", "max_length": 45, "description": "Resolved IP address (IPv4 or IPv6)", "example": "*************", "required": false}, {"name": "first_seen", "type": "datetime", "format": "ISO8601", "description": "First time domain was observed", "example": "2025-08-16T14:30:00.000Z", "required": true}, {"name": "last_seen", "type": "datetime", "format": "ISO8601", "description": "Most recent time domain was observed", "example": "2025-08-16T15:45:00.000Z", "required": true}, {"name": "occurrence_count", "type": "integer", "description": "Total number of times domain has been seen", "example": 15, "required": true, "default": 1}, {"name": "current_risk_level", "type": "string", "max_length": 10, "description": "Current assessed risk level", "allowed_values": ["LOW", "MEDIUM", "HIGH"], "example": "LOW", "required": true, "default": "LOW"}, {"name": "highest_risk_level", "type": "string", "max_length": 10, "description": "Highest risk level ever assigned", "allowed_values": ["LOW", "MEDIUM", "HIGH"], "example": "MEDIUM", "required": true, "default": "LOW"}, {"name": "threat_categories", "type": "string", "max_length": 500, "description": "Semicolon-separated list of threat categories", "example": "suspicious-pattern;long-domain", "required": false}, {"name": "dns_record_type", "type": "string", "max_length": 10, "description": "DNS record type", "example": "A", "required": false, "default": "A"}, {"name": "created_date", "type": "datetime", "format": "ISO8601", "description": "When this record was first created", "example": "2025-08-16T14:30:00.000Z", "required": true}, {"name": "modified_date", "type": "datetime", "format": "ISO8601", "description": "When this record was last modified", "example": "2025-08-16T15:45:00.000Z", "required": true}], "indexes": [{"name": "idx_domain_name", "columns": ["domain_name"], "unique": true, "description": "Primary key index for fast domain lookups"}, {"name": "idx_last_seen", "columns": ["last_seen"], "description": "Index for date range queries"}, {"name": "idx_risk_level", "columns": ["current_risk_level", "highest_risk_level"], "description": "Index for risk-based filtering"}], "maintenance": {"max_records": 10000, "archive_after_days": 365, "cleanup_frequency": "monthly", "backup_frequency": "daily"}, "sql_server_integration": {"table_name": "DNS_DOMAIN_HISTORY", "format_file_path": "data/dns_reports/setup/dns_domain_history_format.fmt", "bulk_insert_command": "BULK INSERT DNS_DOMAIN_HISTORY FROM 'path/to/dns_domain_history.csv' WITH (FORMATFILE = 'path/to/format.fmt', FIRSTROW = 2)"}, "usage_notes": ["File is updated every time DNS cache is processed", "Domain names are stored in lowercase for consistency", "IP addresses may change over time - only current IP is stored", "Threat categories are cumulative and semicolon-separated", "File can be imported into Excel, SQL Server, or other tools", "Regular backups recommended for data preservation"]}