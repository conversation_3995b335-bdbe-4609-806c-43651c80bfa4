{"name": "Test Write Binary File Paths", "nodes": [{"parameters": {}, "id": "manual-trigger", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"jsCode": "// Create test data for different path tests\nconst testContent = `Path Test File\nTimestamp: ${new Date().toISOString()}\nTest: Write Binary File Path Testing\n`;\n\nconst binaryData = {\n  data: Buffer.from(testContent, 'utf8').toString('base64'),\n  mimeType: 'text/plain',\n  fileName: 'path_test.txt',\n  fileExtension: 'txt'\n};\n\nconsole.log('Test data prepared for path testing');\nconsole.log('Working directory:', process.cwd());\n\nreturn [{\n  json: {\n    testContent: testContent,\n    fileName: 'path_test.txt',\n    timestamp: new Date().toISOString()\n  },\n  binary: {\n    data: binaryData\n  }\n}];"}, "id": "prepare-test-data", "name": "Prepare Test Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"fileName": "path_test_root.txt", "dataPropertyName": "data", "options": {"overwrite": true}}, "id": "test-root-path", "name": "Test Root Path", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [680, 200]}, {"parameters": {"fileName": "shared/path_test_shared.txt", "dataPropertyName": "data", "options": {"overwrite": true}}, "id": "test-shared-path", "name": "Test Shared Path", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"fileName": "/home/<USER>/shared/path_test_absolute.txt", "dataPropertyName": "data", "options": {"overwrite": true}}, "id": "test-absolute-path", "name": "Test Absolute Path", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [680, 400]}, {"parameters": {"jsCode": "// Check where files were actually created\nconst fs = require('fs');\nconst data = $input.first().json;\n\nconsole.log('=== FILE LOCATION CHECK ===');\nconsole.log('Working directory:', process.cwd());\n\nconst testPaths = [\n  '/home/<USER>/path_test_root.txt',\n  '/home/<USER>/shared/path_test_root.txt',\n  '/home/<USER>/shared/path_test_shared.txt',\n  '/home/<USER>/shared/path_test_absolute.txt',\n  './path_test_root.txt',\n  './shared/path_test_shared.txt'\n];\n\nconst foundFiles = [];\n\nfor (const testPath of testPaths) {\n  try {\n    if (fs.existsSync(testPath)) {\n      const stats = fs.statSync(testPath);\n      console.log(`✅ FOUND: ${testPath} (${stats.size} bytes)`);\n      foundFiles.push({\n        path: testPath,\n        size: stats.size,\n        created: stats.birthtime\n      });\n    } else {\n      console.log(`❌ NOT FOUND: ${testPath}`);\n    }\n  } catch (error) {\n    console.log(`❌ ERROR checking ${testPath}: ${error.message}`);\n  }\n}\n\n// List all files in key directories\ntry {\n  console.log('\\n📁 Files in /home/<USER>/:');\n  const homeFiles = fs.readdirSync('/home/<USER>/');\n  homeFiles.forEach(file => {\n    if (file.includes('path_test')) {\n      console.log(`   ${file}`);\n    }\n  });\n} catch (error) {\n  console.log('Error listing /home/<USER>/:', error.message);\n}\n\ntry {\n  console.log('\\n📁 Files in /home/<USER>/shared/:');\n  const sharedFiles = fs.readdirSync('/home/<USER>/shared/');\n  sharedFiles.forEach(file => {\n    if (file.includes('path_test')) {\n      console.log(`   ${file}`);\n    }\n  });\n} catch (error) {\n  console.log('Error listing /home/<USER>/shared/:', error.message);\n}\n\nreturn [{\n  json: {\n    ...data,\n    foundFiles: foundFiles,\n    checkComplete: true\n  }\n}];"}, "id": "check-file-locations", "name": "Check File Locations", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Prepare Test Data", "type": "main", "index": 0}]]}, "Prepare Test Data": {"main": [[{"node": "Test Root Path", "type": "main", "index": 0}, {"node": "Test Shared Path", "type": "main", "index": 0}, {"node": "Test Absolute Path", "type": "main", "index": 0}]]}, "Test Root Path": {"main": [[{"node": "Check File Locations", "type": "main", "index": 0}]]}, "Test Shared Path": {"main": [[{"node": "Check File Locations", "type": "main", "index": 0}]]}, "Test Absolute Path": {"main": [[{"node": "Check File Locations", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 1, "updatedAt": "2025-08-15T23:30:00.000Z", "versionId": "1"}