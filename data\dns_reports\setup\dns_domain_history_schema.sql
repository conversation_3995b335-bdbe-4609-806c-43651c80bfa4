-- DNS Domain History SQL Server Integration Schema
-- Optional advanced feature for users with SQL Server access
-- This allows importing the CSV file into SQL Server for advanced analytics

-- Create table for DNS domain history
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'DNS_DOMAIN_HISTORY')
BEGIN
    CREATE TABLE DNS_DOMAIN_HISTORY (
        domain_name NVARCHAR(255) NOT NULL PRIMARY KEY,
        ip_address NVARCHAR(45) NULL,
        first_seen DATETIME2 NOT NULL,
        last_seen DATETIME2 NOT NULL,
        occurrence_count INT NOT NULL DEFAULT 1,
        current_risk_level NVARCHAR(10) NOT NULL DEFAULT 'LOW',
        highest_risk_level NVARCHAR(10) NOT NULL DEFAULT 'LOW',
        threat_categories NVARCHAR(500) NULL,
        dns_record_type NVARCHAR(10) NULL DEFAULT 'A',
        created_date DATETIME2 NOT NULL DEFAULT GETDATE(),
        modified_date DATETIME2 NOT NULL DEFAULT GETDATE()
    );
    
    -- Create indexes for performance
    CREATE INDEX IX_DNS_DOMAIN_HISTORY_LastSeen ON DNS_DOMAIN_HISTORY (last_seen);
    CREATE INDEX IX_DNS_DOMAIN_HISTORY_RiskLevel ON DNS_DOMAIN_HISTORY (current_risk_level, highest_risk_level);
    CREATE INDEX IX_DNS_DOMAIN_HISTORY_FirstSeen ON DNS_DOMAIN_HISTORY (first_seen);
    
    PRINT 'DNS_DOMAIN_HISTORY table created successfully';
END
ELSE
BEGIN
    PRINT 'DNS_DOMAIN_HISTORY table already exists';
END

-- Create format file for BULK INSERT (advanced feature)
-- Note: This would need to be created as a separate .fmt file for BULK INSERT
-- Format file content would be:
-- 12.0
-- 11
-- 1  SQLCHAR  0  255  ","  1  domain_name  SQL_Latin1_General_CP1_CI_AS
-- 2  SQLCHAR  0  45   ","  2  ip_address  SQL_Latin1_General_CP1_CI_AS
-- 3  SQLCHAR  0  30   ","  3  first_seen  ""
-- 4  SQLCHAR  0  30   ","  4  last_seen  ""
-- 5  SQLCHAR  0  10   ","  5  occurrence_count  ""
-- 6  SQLCHAR  0  10   ","  6  current_risk_level  SQL_Latin1_General_CP1_CI_AS
-- 7  SQLCHAR  0  10   ","  7  highest_risk_level  SQL_Latin1_General_CP1_CI_AS
-- 8  SQLCHAR  0  500  ","  8  threat_categories  SQL_Latin1_General_CP1_CI_AS
-- 9  SQLCHAR  0  10   ","  9  dns_record_type  SQL_Latin1_General_CP1_CI_AS
-- 10 SQLCHAR  0  30   ","  10 created_date  ""
-- 11 SQLCHAR  0  30   "\n" 11 modified_date  ""

-- Sample queries for domain analysis
-- Uncomment and run these for analytics:

/*
-- Domains seen in last 24 hours
SELECT domain_name, ip_address, last_seen, current_risk_level
FROM DNS_DOMAIN_HISTORY 
WHERE last_seen >= DATEADD(hour, -24, GETDATE())
ORDER BY last_seen DESC;

-- High-risk domains ever detected
SELECT domain_name, ip_address, first_seen, last_seen, highest_risk_level, threat_categories
FROM DNS_DOMAIN_HISTORY 
WHERE highest_risk_level = 'HIGH'
ORDER BY last_seen DESC;

-- Most frequently accessed domains
SELECT TOP 20 domain_name, occurrence_count, last_seen, current_risk_level
FROM DNS_DOMAIN_HISTORY 
ORDER BY occurrence_count DESC;

-- Domain risk level changes over time
SELECT 
    domain_name,
    first_seen,
    last_seen,
    current_risk_level,
    highest_risk_level,
    CASE 
        WHEN current_risk_level != highest_risk_level THEN 'Risk Level Changed'
        ELSE 'Consistent Risk'
    END as risk_status
FROM DNS_DOMAIN_HISTORY
WHERE highest_risk_level != 'LOW'
ORDER BY last_seen DESC;

-- Domains by TLD (top-level domain) analysis
SELECT 
    RIGHT(domain_name, LEN(domain_name) - CHARINDEX('.', REVERSE(domain_name)) + 1) as tld,
    COUNT(*) as domain_count,
    AVG(occurrence_count) as avg_occurrences,
    SUM(CASE WHEN current_risk_level = 'HIGH' THEN 1 ELSE 0 END) as high_risk_count
FROM DNS_DOMAIN_HISTORY
GROUP BY RIGHT(domain_name, LEN(domain_name) - CHARINDEX('.', REVERSE(domain_name)) + 1)
ORDER BY domain_count DESC;
*/

-- Maintenance procedures
-- Clean up old records (older than 1 year)
/*
DELETE FROM DNS_DOMAIN_HISTORY 
WHERE last_seen < DATEADD(year, -1, GETDATE())
AND highest_risk_level = 'LOW';
*/

-- Update statistics for better query performance
/*
UPDATE STATISTICS DNS_DOMAIN_HISTORY;
*/
