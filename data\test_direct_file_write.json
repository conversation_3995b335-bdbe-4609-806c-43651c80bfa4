{"name": "Test Direct File Write", "nodes": [{"parameters": {}, "id": "manual-trigger", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"jsCode": "// Test direct file writing using fs module\nconsole.log('=== DIRECT FILE WRITE TEST ===');\n\ntry {\n  const fs = require('fs');\n  const path = require('path');\n  \n  // Test content\n  const testContent = `Direct File Write Test\nTimestamp: ${new Date().toISOString()}\nWorking Directory: ${process.cwd()}\nNode Version: ${process.version}\n`;\n  \n  console.log('Test content prepared');\n  console.log('Working directory:', process.cwd());\n  \n  // Try different paths\n  const testPaths = [\n    '/home/<USER>/shared/direct_test.txt',\n    './direct_test.txt',\n    '/tmp/direct_test.txt'\n  ];\n  \n  const results = [];\n  \n  for (const testPath of testPaths) {\n    try {\n      console.log(`\\nTrying to write to: ${testPath}`);\n      \n      // Ensure directory exists\n      const dir = path.dirname(testPath);\n      if (!fs.existsSync(dir)) {\n        console.log(`Creating directory: ${dir}`);\n        fs.mkdirSync(dir, { recursive: true });\n      }\n      \n      // Write file\n      fs.writeFileSync(testPath, testContent, 'utf8');\n      \n      // Verify file exists\n      if (fs.existsSync(testPath)) {\n        const stats = fs.statSync(testPath);\n        console.log(`✅ SUCCESS: File written to ${testPath}`);\n        console.log(`   Size: ${stats.size} bytes`);\n        console.log(`   Created: ${stats.birthtime}`);\n        \n        results.push({\n          path: testPath,\n          success: true,\n          size: stats.size,\n          created: stats.birthtime\n        });\n      } else {\n        console.log(`❌ FAILED: File not found after write: ${testPath}`);\n        results.push({\n          path: testPath,\n          success: false,\n          error: 'File not found after write'\n        });\n      }\n      \n    } catch (error) {\n      console.log(`❌ ERROR writing to ${testPath}: ${error.message}`);\n      results.push({\n        path: testPath,\n        success: false,\n        error: error.message\n      });\n    }\n  }\n  \n  // List files in shared directory\n  try {\n    if (fs.existsSync('/home/<USER>/shared')) {\n      console.log('\\n📁 Files in /home/<USER>/shared:');\n      const files = fs.readdirSync('/home/<USER>/shared');\n      files.forEach(file => {\n        const filePath = path.join('/home/<USER>/shared', file);\n        const stats = fs.statSync(filePath);\n        console.log(`   ${file} (${stats.size} bytes)`);\n      });\n    } else {\n      console.log('❌ /home/<USER>/shared directory does not exist');\n    }\n  } catch (error) {\n    console.log('Error listing shared directory:', error.message);\n  }\n  \n  return [{\n    json: {\n      testResults: results,\n      workingDirectory: process.cwd(),\n      nodeVersion: process.version,\n      timestamp: new Date().toISOString()\n    }\n  }];\n  \n} catch (error) {\n  console.log('❌ CRITICAL ERROR:', error.message);\n  return [{\n    json: {\n      error: error.message,\n      timestamp: new Date().toISOString()\n    }\n  }];\n}"}, "id": "direct-file-write", "name": "Direct File Write Test", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Direct File Write Test", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 1, "updatedAt": "2025-08-15T22:30:00.000Z", "versionId": "1"}