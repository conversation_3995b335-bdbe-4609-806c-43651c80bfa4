@echo off
REM DNS Cache Collection Batch File for N8N Docker Integration
REM This file runs on the Windows host to collect DNS cache data

echo [%date% %time%] Starting DNS cache collection...

REM Stay in the setup directory where all DNS scripts are now located
cd /d "%~dp0"

REM Check if we're in the right directory
if not exist "get_dns_cache.ps1" (
    echo ERROR: Cannot find get_dns_cache.ps1
    echo Current directory: %CD%
    echo Expected to be in data\dns_reports\setup directory
    exit /b 1
)

echo Found DNS setup directory: %CD%

REM Execute the PowerShell script to collect DNS cache
echo Running PowerShell DNS cache collection...
powershell.exe -ExecutionPolicy Bypass -File "get_dns_cache.ps1"

REM Check if the PowerShell script succeeded
if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: DNS cache collection completed
    echo Output file: data\dns_reports\setup\dns_cache_output.txt
) else (
    echo ERROR: DNS cache collection failed with error code %ERRORLEVEL%
    exit /b %ERRORLEVEL%
)

REM Verify the output file was created
if exist "data\dns_reports\setup\dns_cache_output.txt" (
    for %%A in ("data\dns_reports\setup\dns_cache_output.txt") do (
        echo File size: %%~zA bytes
        echo File timestamp: %%~tA
    )
    echo [%date% %time%] DNS cache collection completed successfully
    exit /b 0
) else (
    echo ERROR: DNS cache output file was not created
    exit /b 1
)
