# PowerShell script to save DNS security reports
# Called by N8N workflow via HTTP Request or Execute Command

param(
    [Parameter(Mandatory=$true)]
    [string]$Content,
    
    [Parameter(Mandatory=$true)]
    [string]$FileName,
    
    [Parameter(Mandatory=$false)]
    [string]$ReportType = "html"
)

# Create reports directory if it doesn't exist
$ReportsDir = "C:\DNS_Security_Reports"
if (-not (Test-Path $ReportsDir)) {
    New-Item -ItemType Directory -Path $ReportsDir -Force
    Write-Host "Created reports directory: $ReportsDir"
}

# Full file path
$FilePath = Join-Path $ReportsDir $FileName

try {
    # Save the content to file
    $Content | Out-File -FilePath $FilePath -Encoding UTF8 -Force
    
    Write-Host "✅ Report saved successfully!"
    Write-Host "   File: $FilePath"
    Write-Host "   Size: $((Get-Item $FilePath).Length) bytes"
    Write-Host "   Type: $ReportType"
    
    # If it's an HTML report, optionally open it
    if ($ReportType -eq "html") {
        $OpenFile = Read-Host "Open report in browser? (y/N)"
        if ($OpenFile -eq 'y' -or $OpenFile -eq 'Y') {
            Start-Process $FilePath
        }
        
        Write-Host ""
        Write-Host "🌐 To view the report, open: file:///$($FilePath.Replace('\', '/'))"
    }
    
    # Return success info as JSON for N8N
    $Result = @{
        success = $true
        filePath = $FilePath
        fileName = $FileName
        fileSize = (Get-Item $FilePath).Length
        timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        url = "file:///$($FilePath.Replace('\', '/'))"
    }
    
    Write-Output ($Result | ConvertTo-Json)
    
} catch {
    Write-Error "❌ Failed to save report: $($_.Exception.Message)"
    
    # Return error info as JSON
    $ErrorResult = @{
        success = $false
        error = $_.Exception.Message
        filePath = $FilePath
        timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    }
    
    Write-Output ($ErrorResult | ConvertTo-Json)
    exit 1
}
