# Test the DNS report file saving functionality

Write-Host "🧪 Testing DNS Report File Saving" -ForegroundColor Cyan
Write-Host "=" * 40

# Test HTML content
$TestHtmlContent = @"
<!DOCTYPE html>
<html>
<head>
    <title>DNS Security Report Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .high-risk { color: red; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🛡️ DNS Security Test Report</h1>
    <p>Generated: $(Get-Date)</p>
    <div class="high-risk">⚠️ Test high-risk domain: suspicious-test-domain.com</div>
    <p>This is a test report to verify file saving functionality.</p>
</body>
</html>
"@

$TestFileName = "test_dns_report_$(Get-Date -Format 'yyyy-MM-dd_HHmmss').html"

Write-Host "Test file name: $TestFileName" -ForegroundColor Yellow
Write-Host "Content length: $($TestHtmlContent.Length) characters" -ForegroundColor Yellow

# Test the save script
try {
    Write-Host "`nCalling save script..." -ForegroundColor Green
    
    $Result = & ".\Scripts\save_dns_report.ps1" -Content $TestHtmlContent -FileName $TestFileName -ReportType "html"
    
    Write-Host "✅ Save script executed successfully!" -ForegroundColor Green
    Write-Host "Result: $Result" -ForegroundColor White
    
    # Parse the JSON result
    $ResultObj = $Result | ConvertFrom-Json
    
    if ($ResultObj.success) {
        Write-Host "`n🎉 File saved successfully!" -ForegroundColor Green
        Write-Host "   File path: $($ResultObj.filePath)" -ForegroundColor White
        Write-Host "   File size: $($ResultObj.fileSize) bytes" -ForegroundColor White
        Write-Host "   URL: $($ResultObj.url)" -ForegroundColor White
        
        # Verify file exists
        if (Test-Path $ResultObj.filePath) {
            Write-Host "✅ File verification: EXISTS" -ForegroundColor Green
            
            $OpenNow = Read-Host "`nOpen the test report in browser? (y/N)"
            if ($OpenNow -eq 'y' -or $OpenNow -eq 'Y') {
                Start-Process $ResultObj.filePath
                Write-Host "🌐 Report opened in default browser" -ForegroundColor Green
            }
        } else {
            Write-Host "❌ File verification: NOT FOUND" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Save failed: $($ResultObj.error)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Error testing save script: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n📋 Summary:" -ForegroundColor Cyan
Write-Host "- The save script creates files in C:\DNS_Security_Reports\" -ForegroundColor White
Write-Host "- HTML reports can be opened directly in any browser" -ForegroundColor White
Write-Host "- File URLs use the format: file:///C:/DNS_Security_Reports/filename.html" -ForegroundColor White
Write-Host "- N8N workflow will call this script to save actual reports" -ForegroundColor White
