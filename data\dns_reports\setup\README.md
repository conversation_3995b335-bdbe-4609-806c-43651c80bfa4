﻿# DNS Security Monitor Setup

This folder contains configuration and setup files for the N8N DNS Security Monitor system.

## 📁 Files in this Directory

- **`dns_cache_output.txt`** - Raw Windows DNS cache data collected by PowerShell script
- **`dns_cache_metadata.json`** - Metadata about DNS cache collection (timestamp, file size, etc.)
- **`dns_domain_history.csv`** - Historical domain tracking database (flat file format)
- **`dns_domain_history_format.json`** - Format specification for the history file
- **`dns_domain_history_schema.sql`** - Optional SQL Server integration schema
- **`dns_security_workflow_fixed.json`** - N8N workflow definition with history tracking
- **`README.md`** - This documentation file

## 🚀 Quick Setup

### 1. Automated Setup (Recommended)
```powershell
# Run from N8N_Builder root directory
.\data\dns_reports\setup\setup_dns_monitoring.ps1
```

Choose option 1 for Windows Task Scheduler automation.

### 2. Manual DNS Cache Collection
```powershell
# Run from N8N_Builder root directory
.\Scripts\get_dns_cache.ps1
```

### 3. Import N8N Workflow
1. Open N8N at http://localhost:5678
2. Import: `data/dns_reports/setup/dns_security_workflow_fixed.json`
3. Run the workflow manually or let it run every 60 minutes

## 🔄 How It Works

1. **DNS Cache Collection**: PowerShell script runs `ipconfig /displaydns` and saves output to `dns_cache_output.txt`
2. **Domain History Tracking**: Automatically updates `dns_domain_history.csv` with domain occurrence counts, first/last seen dates
3. **N8N Processing**: Workflow reads DNS cache and historical data, analyzes domains for security threats
4. **Report Generation**: Creates HTML reports and alert files in the parent `dns_reports/` directory with historical context
5. **Dashboard Access**: View results via `../index.html`

## 📋 Automation Options

### Windows Task Scheduler (Recommended)
- Runs every 60 minutes automatically (synchronized with N8N)
- Collects DNS cache and updates domain history
- Starts with Windows
- No manual intervention required

### Continuous Loop Script
- Runs in PowerShell window
- Good for testing and monitoring
- Must keep window open

### Manual Collection
- Run script manually before each N8N workflow execution
- Full control over timing
- Good for troubleshooting

## 🛠️ Troubleshooting

### DNS Cache File Not Found
- Ensure PowerShell script ran successfully
- Check file exists: `data/dns_reports/setup/dns_cache_output.txt`
- Run: `.\Scripts\get_dns_cache.ps1` manually

### N8N Workflow Errors
- Verify file path in Read DNS File node: `/home/<USER>/shared/dns_reports/setup/dns_cache_output.txt`
- Check Docker volume mount is working
- Ensure dns_reports directory exists

### Permission Issues
- Run: `.\Scripts\fix_permissions.ps1`
- Ensure Docker container can access shared directory
- Check file ownership (should be node:node in container)

## 📊 Expected Output

### Successful DNS Collection
```
Getting Windows DNS cache...
SUCCESS: DNS cache saved to: data\dns_reports\setup\dns_cache_output.txt
   File size: 9102 bytes
   Lines: 232
SUCCESS: Metadata saved to: data\dns_reports\setup\dns_cache_metadata.json

Updating domain history...
Found 8 unique domains
Loaded 10 existing domain records
Domain history update complete:
  New domains: 0
  Updated domains: 8
  Total domains: 10

Sample DNS entries found:
   - google.com
   - github.com
   - microsoft.com
```

### N8N Workflow Results
- **HTML Report**: `../dns_security_report_YYYY-MM-DD.html`
- **Alert Files**: `../DNS_SECURITY_ALERT_YYYY-MM-DD_timestamp.txt` (if high-risk domains found)
- **Dashboard**: `../index.html` (updated with links to latest reports)
- **Domain History**: `dns_domain_history.csv` (continuously updated with domain tracking data)

## 📊 Domain History Tracking

The system automatically maintains a historical record of all domains accessed:

### History File Format
- **File**: `dns_domain_history.csv` (flat file, portable)
- **Format**: CSV with headers, UTF-8 encoding
- **Updates**: Existing rows updated (no duplicates), new domains added
- **Tracking**: First seen, last seen, occurrence counts, risk levels

### Sample History Data
```csv
domain_name,occurrence_count,first_seen,last_seen,current_risk_level
google.com,15,2025-08-16T12:00:00Z,2025-08-17T14:07:32Z,LOW
suspicious-domain.com,3,2025-08-17T10:30:00Z,2025-08-17T14:07:32Z,HIGH
```

### Query Tools
```powershell
# View summary statistics
.\data\dns_reports\setup\query_domain_history.ps1 -Query summary

# Show recent domains (last 7 days)
.\data\dns_reports\setup\query_domain_history.ps1 -Query recent -Days 7

# Find high-risk domains
.\data\dns_reports\setup\query_domain_history.ps1 -Query highrisk

# Search for specific domain
.\data\dns_reports\setup\query_domain_history.ps1 -Query search -Domain "google"

# Export filtered data
.\data\dns_reports\setup\query_domain_history.ps1 -Query export -RiskLevel HIGH
```

### Advanced Integration (Optional)
- **SQL Server**: Use `dns_domain_history_schema.sql` for database import
- **Excel**: Open CSV directly for analysis and charts
- **Python/R**: Import CSV for data science and trend analysis

## 🎯 Next Steps

1. Set up automation using `.\data\dns_reports\setup\setup_dns_monitoring.ps1`
2. Import and run the N8N workflow
3. Access dashboard at `data/dns_reports/index.html`
4. Monitor for security alerts and review reports regularly

## 📞 Support

For issues or questions:
1. Check this README for troubleshooting steps
2. Review N8N workflow console logs
3. Verify PowerShell script execution
4. Ensure Docker volume mounts are working correctly
