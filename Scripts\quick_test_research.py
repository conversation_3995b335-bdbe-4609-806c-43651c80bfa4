#!/usr/bin/env python3
"""
Quick test of N8N research functionality
"""

import asyncio
import sys
from pathlib import Path

# Add the parent directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from n8n_builder.mcp_research_tool import N8NResearchTool

async def quick_test():
    print("🔍 Quick N8N Research Test")
    print("=" * 30)
    
    # Test with simple cache
    research_tool = N8NResearchTool(
        cache_ttl=60,
        enable_enhanced_cache=False,
        github_api_token=None
    )
    
    async with research_tool:
        print("Testing HTTP Request node search...")
        results = await research_tool.search_n8n_docs(
            query="HTTP Request node",
            node_type="HTTP Request"
        )
        
        print(f"Found {len(results)} results:")
        for i, result in enumerate(results, 1):
            print(f"{i}. {result.title}")
            print(f"   URL: {result.url}")
            print(f"   Content: {result.content[:200]}...")
            print()

if __name__ == "__main__":
    asyncio.run(quick_test())
