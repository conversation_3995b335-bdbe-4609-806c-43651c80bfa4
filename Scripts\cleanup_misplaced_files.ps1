# Clean up files that were created in wrong location
Write-Host "Cleaning up misplaced N8N files..." -ForegroundColor Cyan

Set-Location "n8n-docker"

# Remove files from /home/<USER>/ that should be in /home/<USER>/shared/
Write-Host "Removing misplaced files from container..." -ForegroundColor Yellow

try {
    # Remove DNS security files from wrong location
    docker-compose exec n8n rm -f /home/<USER>/DNS_SECURITY_ALERT_*.txt
    docker-compose exec n8n rm -f /home/<USER>/dns_security_report_*.html
    docker-compose exec n8n rm -f /home/<USER>/n8n_test_file.txt
    
    Write-Host "✅ Cleaned up misplaced files" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Could not clean up files: $($_.Exception.Message)" -ForegroundColor Yellow
}

Set-Location ".."
Write-Host "Cleanup complete!" -ForegroundColor Green
