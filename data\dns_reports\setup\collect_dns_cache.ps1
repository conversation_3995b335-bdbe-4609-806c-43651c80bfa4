# DNS Cache Collection PowerShell Script for N8N Docker Integration
# This script can be called from the Docker container to collect DNS cache on Windows host

param(
    [switch]$Verbose
)

$ErrorActionPreference = "Stop"

if ($Verbose) {
    Write-Host "[$(Get-Date)] Starting DNS cache collection from Docker..." -ForegroundColor Cyan
}

try {
    # Determine the project root directory
    $scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
    $projectRoot = Split-Path -Parent (Split-Path -Parent $scriptDir)
    
    if ($Verbose) {
        Write-Host "Script directory: $scriptDir" -ForegroundColor Gray
        Write-Host "Project root: $projectRoot" -ForegroundColor Gray
    }
    
    # Change to project root
    Set-Location $projectRoot
    
    # Verify we're in the right location
    if (-not (Test-Path "Scripts\get_dns_cache.ps1")) {
        throw "Cannot find Scripts\get_dns_cache.ps1 in $projectRoot"
    }
    
    if ($Verbose) {
        Write-Host "Found project directory: $projectRoot" -ForegroundColor Green
    }
    
    # Execute the main DNS cache collection script
    & ".\get_dns_cache.ps1"
    
    # Verify the output file was created
    $outputFile = "dns_cache_output.txt"
    if (Test-Path $outputFile) {
        $fileInfo = Get-Item $outputFile
        if ($Verbose) {
            Write-Host "SUCCESS: DNS cache file created" -ForegroundColor Green
            Write-Host "  File: $outputFile" -ForegroundColor White
            Write-Host "  Size: $($fileInfo.Length) bytes" -ForegroundColor White
            Write-Host "  Modified: $($fileInfo.LastWriteTime)" -ForegroundColor White
        }
        
        # Return success status for N8N
        Write-Output "DNS_CACHE_COLLECTION_SUCCESS"
        Write-Output "FILE_SIZE:$($fileInfo.Length)"
        Write-Output "TIMESTAMP:$($fileInfo.LastWriteTime.ToString('yyyy-MM-ddTHH:mm:ss'))"
        
    } else {
        throw "DNS cache output file was not created: $outputFile"
    }
    
} catch {
    Write-Error "DNS cache collection failed: $($_.Exception.Message)"
    Write-Output "DNS_CACHE_COLLECTION_FAILED"
    Write-Output "ERROR:$($_.Exception.Message)"
    exit 1
}
