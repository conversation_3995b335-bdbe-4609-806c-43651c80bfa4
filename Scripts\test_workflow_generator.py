#!/usr/bin/env python3
"""
Test script for N8N Workflow Generator
Tests the workflow generation capabilities and N8N instance connection
"""

import asyncio
import sys
import json
from pathlib import Path

# Add the parent directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from n8n_builder.config import config

async def test_n8n_connection():
    """Test connection to N8N instance."""
    print("🔗 Testing N8N Instance Connection")
    print("=" * 40)
    
    import httpx
    
    n8n_url = "http://localhost:5678"
    
    try:
        async with httpx.AsyncClient() as client:
            # Test basic connection
            response = await client.get(f"{n8n_url}/healthz", timeout=10)
            
            if response.status_code == 200:
                print(f"✅ N8N instance is running at {n8n_url}")
                print(f"   Status: {response.status_code}")
                return True
            else:
                print(f"⚠️  N8N instance responded with status: {response.status_code}")
                return False
                
    except httpx.ConnectError:
        print(f"❌ Cannot connect to N8N instance at {n8n_url}")
        print("   Make sure N8<PERSON> is running with: docker-compose up -d")
        return False
    except Exception as e:
        print(f"❌ Error connecting to N8N: {e}")
        return False

def test_llm_config():
    """Test LLM configuration."""
    print("\n🤖 Testing LLM Configuration")
    print("=" * 40)
    
    llm_config = config.mimo_llm
    
    print(f"Endpoint: {llm_config.endpoint}")
    print(f"Model: {llm_config.model}")
    print(f"Temperature: {llm_config.temperature}")
    print(f"Max Tokens: {llm_config.max_tokens}")
    print(f"Is Local: {llm_config.is_local}")
    
    if llm_config.is_local:
        print("✅ Using local LLM (LM Studio)")
        if "localhost:1234" in llm_config.endpoint:
            print("✅ LM Studio endpoint configured correctly")
        else:
            print("⚠️  Unexpected local endpoint")
    else:
        print("⚠️  Using remote LLM")
    
    return True

async def test_workflow_generation():
    """Test basic workflow generation."""
    print("\n🔧 Testing Workflow Generation")
    print("=" * 40)
    
    try:
        from n8n_builder.n8n_builder import N8NBuilder
        
        # Initialize builder
        builder = N8NBuilder()
        print("✅ N8N Builder initialized successfully")
        
        # Test simple workflow generation
        print("\nGenerating test workflow...")
        
        test_description = "Create a simple workflow that triggers manually and sends a message to Slack"
        
        try:
            workflow_result = builder.generate_workflow(
                plain_english_description=test_description
            )

            # The method returns a JSON string, so we need to parse it
            if workflow_result:
                import json
                try:
                    workflow_data = json.loads(workflow_result)
                    workflow = workflow_data
                except json.JSONDecodeError:
                    # If it's not JSON, treat the result as the workflow directly
                    workflow = {"nodes": [], "connections": {}}
                    print(f"⚠️  Workflow result is not valid JSON: {workflow_result[:200]}...")
            else:
                workflow = None

            if workflow and isinstance(workflow, dict):
                print("✅ Workflow generated successfully!")
                print(f"   Nodes: {len(workflow.get('nodes', []))}")
                print(f"   Connections: {len(workflow.get('connections', {}))}")

                # Show node types
                node_types = [node.get('type', 'Unknown') for node in workflow.get('nodes', [])]
                print(f"   Node types: {', '.join(node_types)}")

                return True
            else:
                print("❌ Workflow generation failed - no workflow returned")
                return False
                
        except Exception as e:
            print(f"❌ Workflow generation failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to initialize N8N Builder: {e}")
        return False

async def test_mcp_integration():
    """Test MCP integration with workflow generation."""
    print("\n🔍 Testing MCP Integration")
    print("=" * 40)
    
    try:
        # Test the MCP research functionality we just fixed
        from n8n_builder.mcp_research_tool import N8NResearchTool
        
        research_tool = N8NResearchTool(cache_ttl=60, enable_enhanced_cache=False)
        
        async with research_tool:
            results = await research_tool.search_n8n_docs(
                query="manual trigger node",
                node_type="Manual Trigger"
            )
            
            if results:
                print(f"✅ MCP research working - found {len(results)} results")
                print(f"   First result: {results[0].title}")
                return True
            else:
                print("⚠️  MCP research returned no results")
                return False
                
    except Exception as e:
        print(f"❌ MCP integration test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🚀 N8N Workflow Generator Test Suite")
    print("=" * 50)
    
    results = {}
    
    # Test N8N connection
    results['n8n_connection'] = await test_n8n_connection()
    
    # Test LLM configuration
    results['llm_config'] = test_llm_config()
    
    # Test MCP integration
    results['mcp_integration'] = await test_mcp_integration()
    
    # Test workflow generation (only if other tests pass)
    if results['llm_config'] and results['mcp_integration']:
        results['workflow_generation'] = await test_workflow_generation()
    else:
        print("\n⚠️  Skipping workflow generation test due to failed prerequisites")
        results['workflow_generation'] = False
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 30)
    
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"\nOverall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! N8N MCP integration is ready.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    asyncio.run(main())
