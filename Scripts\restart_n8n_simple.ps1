# Simple N8N Restart Script for Volume Mount Fix
Write-Host "Restarting N8N with Volume Mount..." -ForegroundColor Green

# Change to n8n-docker directory
Set-Location "n8n-docker"

# Stop containers
Write-Host "Stopping containers..." -ForegroundColor Yellow
docker-compose down --remove-orphans

# Wait a moment
Start-Sleep -Seconds 5

# Start containers
Write-Host "Starting containers..." -ForegroundColor Yellow
docker-compose up -d

# Wait for startup
Write-Host "Waiting for startup..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# Check status
Write-Host "Container Status:" -ForegroundColor Cyan
docker-compose ps

# Test volume mount
Write-Host "Testing volume mount..." -ForegroundColor Yellow
try {
    docker-compose exec n8n touch /home/<USER>/shared/test.txt
    if (Test-Path "../data/test.txt") {
        Write-Host "SUCCESS: Volume mount working!" -ForegroundColor Green
        Remove-Item "../data/test.txt" -Force
    } else {
        Write-Host "ERROR: Volume mount not working" -ForegroundColor Red
    }
} catch {
    Write-Host "Could not test volume mount" -ForegroundColor Yellow
}

# Return to parent directory
Set-Location ".."

Write-Host "Done! Check N8N at http://localhost:5678" -ForegroundColor Green
