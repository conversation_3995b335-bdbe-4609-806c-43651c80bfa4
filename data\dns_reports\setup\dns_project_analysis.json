﻿{
    "ProposedMoves":  [
                          {
                              "CurrentPath":  "Scripts\\analyze_dns_project_files.ps1",
                              "ProposedPath":  "data\\dns_reports\\setup\\analyze_dns_project_files.ps1",
                              "Reason":  "Centralize DNS project files"
                          },
                          {
                              "CurrentPath":  "Scripts\\create_dns_reports_directory.ps1",
                              "ProposedPath":  "data\\dns_reports\\setup\\create_dns_reports_directory.ps1",
                              "Reason":  "Centralize DNS project files"
                          },
                          {
                              "CurrentPath":  "Scripts\\dns_cache_loop.ps1",
                              "ProposedPath":  "data\\dns_reports\\setup\\dns_cache_loop.ps1",
                              "Reason":  "Centralize DNS project files"
                          },
                          {
                              "CurrentPath":  "Scripts\\save_dns_report.ps1",
                              "ProposedPath":  "data\\dns_reports\\setup\\save_dns_report.ps1",
                              "Reason":  "Centralize DNS project files"
                          },
                          {
                              "CurrentPath":  "Scripts\\setup_dns_automation.ps1",
                              "ProposedPath":  "data\\dns_reports\\setup\\setup_dns_automation.ps1",
                              "Reason":  "Centralize DNS project files"
                          },
                          {
                              "CurrentPath":  "Scripts\\setup_dns_monitoring.ps1",
                              "ProposedPath":  "data\\dns_reports\\setup\\setup_dns_monitoring.ps1",
                              "Reason":  "Centralize DNS project files"
                          },
                          {
                              "CurrentPath":  "Scripts\\test_dns_workflow.py",
                              "ProposedPath":  "data\\dns_reports\\setup\\test_dns_workflow.py",
                              "Reason":  "Centralize DNS project files"
                          },
                          {
                              "CurrentPath":  "Scripts\\query_domain_history.ps1",
                              "ProposedPath":  "data\\dns_reports\\setup\\query_domain_history.ps1",
                              "Reason":  "Centralize DNS project files"
                          },
                          {
                              "CurrentPath":  "Scripts\\update_domain_history.ps1",
                              "ProposedPath":  "data\\dns_reports\\setup\\update_domain_history.ps1",
                              "Reason":  "Centralize DNS project files"
                          }
                      ],
    "DNSFiles":  [
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\analyze_dns_project_files.ps1",
                         "RelativePath":  "Scripts\\analyze_dns_project_files.ps1",
                         "Directory":  "Scripts",
                         "Name":  "analyze_dns_project_files.ps1",
                         "Size":  6257,
                         "LastModified":  "\/Date(1755463314516)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\create_dns_reports_directory.ps1",
                         "RelativePath":  "Scripts\\create_dns_reports_directory.ps1",
                         "Directory":  "Scripts",
                         "Name":  "create_dns_reports_directory.ps1",
                         "Size":  1947,
                         "LastModified":  "\/Date(1755354470485)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\dns_cache_loop.ps1",
                         "RelativePath":  "Scripts\\dns_cache_loop.ps1",
                         "Directory":  "Scripts",
                         "Name":  "dns_cache_loop.ps1",
                         "Size":  1694,
                         "LastModified":  "\/Date(1755374011791)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\save_dns_report.ps1",
                         "RelativePath":  "Scripts\\save_dns_report.ps1",
                         "Directory":  "Scripts",
                         "Name":  "save_dns_report.ps1",
                         "Size":  2029,
                         "LastModified":  "\/Date(1755275230958)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\setup_dns_automation.ps1",
                         "RelativePath":  "Scripts\\setup_dns_automation.ps1",
                         "Directory":  "Scripts",
                         "Name":  "setup_dns_automation.ps1",
                         "Size":  5398,
                         "LastModified":  "\/Date(1755374000531)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\setup_dns_monitoring.ps1",
                         "RelativePath":  "Scripts\\setup_dns_monitoring.ps1",
                         "Directory":  "Scripts",
                         "Name":  "setup_dns_monitoring.ps1",
                         "Size":  4853,
                         "LastModified":  "\/Date(1755374040862)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\test_dns_workflow.py",
                         "RelativePath":  "Scripts\\test_dns_workflow.py",
                         "Directory":  "Scripts",
                         "Name":  "test_dns_workflow.py",
                         "Size":  3319,
                         "LastModified":  "\/Date(1755272233626)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\query_domain_history.ps1",
                         "RelativePath":  "Scripts\\query_domain_history.ps1",
                         "Directory":  "Scripts",
                         "Name":  "query_domain_history.ps1",
                         "Size":  5689,
                         "LastModified":  "\/Date(1755446177414)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Scripts\\update_domain_history.ps1",
                         "RelativePath":  "Scripts\\update_domain_history.ps1",
                         "Directory":  "Scripts",
                         "Name":  "update_domain_history.ps1",
                         "Size":  4809,
                         "LastModified":  "\/Date(1755446134464)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\dns_reports\\dns_security_report_2025-08-16.html",
                         "RelativePath":  "data\\dns_reports\\dns_security_report_2025-08-16.html",
                         "Directory":  "dns_reports",
                         "Name":  "dns_security_report_2025-08-16.html",
                         "Size":  4112,
                         "LastModified":  "\/Date(1755385236053)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\dns_reports\\dns_security_report_2025-08-17.html",
                         "RelativePath":  "data\\dns_reports\\dns_security_report_2025-08-17.html",
                         "Directory":  "dns_reports",
                         "Name":  "dns_security_report_2025-08-17.html",
                         "Size":  4111,
                         "LastModified":  "\/Date(1755460809051)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\dns_reports\\Hold\\dns_security_report_2025-08-16.html",
                         "RelativePath":  "data\\dns_reports\\Hold\\dns_security_report_2025-08-16.html",
                         "Directory":  "Hold",
                         "Name":  "dns_security_report_2025-08-16.html",
                         "Size":  4185,
                         "LastModified":  "\/Date(1755378036051)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\dns_reports\\Hold\\dns_system_error_report_2025-08-16.html",
                         "RelativePath":  "data\\dns_reports\\Hold\\dns_system_error_report_2025-08-16.html",
                         "Directory":  "Hold",
                         "Name":  "dns_system_error_report_2025-08-16.html",
                         "Size":  3764,
                         "LastModified":  "\/Date(1755372554318)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\dns_reports\\setup\\collect_dns_cache.bat",
                         "RelativePath":  "data\\dns_reports\\setup\\collect_dns_cache.bat",
                         "Directory":  "setup",
                         "Name":  "collect_dns_cache.bat",
                         "Size":  1464,
                         "LastModified":  "\/Date(1755367719507)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\dns_reports\\setup\\collect_dns_cache.ps1",
                         "RelativePath":  "data\\dns_reports\\setup\\collect_dns_cache.ps1",
                         "Directory":  "setup",
                         "Name":  "collect_dns_cache.ps1",
                         "Size":  2191,
                         "LastModified":  "\/Date(1755462746818)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\dns_reports\\setup\\dns_cache_metadata.json",
                         "RelativePath":  "data\\dns_reports\\setup\\dns_cache_metadata.json",
                         "Directory":  "setup",
                         "Name":  "dns_cache_metadata.json",
                         "Size":  241,
                         "LastModified":  "\/Date(1755462545063)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\dns_reports\\setup\\dns_cache_output.txt",
                         "RelativePath":  "data\\dns_reports\\setup\\dns_cache_output.txt",
                         "Directory":  "setup",
                         "Name":  "dns_cache_output.txt",
                         "Size":  4750,
                         "LastModified":  "\/Date(1755462545048)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\dns_reports\\setup\\dns_domain_history.csv",
                         "RelativePath":  "data\\dns_reports\\setup\\dns_domain_history.csv",
                         "Directory":  "setup",
                         "Name":  "dns_domain_history.csv",
                         "Size":  2295,
                         "LastModified":  "\/Date(1755462545121)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\dns_reports\\setup\\dns_domain_history_format.json",
                         "RelativePath":  "data\\dns_reports\\setup\\dns_domain_history_format.json",
                         "Directory":  "setup",
                         "Name":  "dns_domain_history_format.json",
                         "Size":  4110,
                         "LastModified":  "\/Date(1755445991786)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\dns_reports\\setup\\dns_domain_history_schema.sql",
                         "RelativePath":  "data\\dns_reports\\setup\\dns_domain_history_schema.sql",
                         "Directory":  "setup",
                         "Name":  "dns_domain_history_schema.sql",
                         "Size":  4035,
                         "LastModified":  "\/Date(1755446022285)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\dns_reports\\setup\\dns_security_workflow_design.md",
                         "RelativePath":  "data\\dns_reports\\setup\\dns_security_workflow_design.md",
                         "Directory":  "setup",
                         "Name":  "dns_security_workflow_design.md",
                         "Size":  6025,
                         "LastModified":  "\/Date(1755272384486)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                         "RelativePath":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                         "Directory":  "setup",
                         "Name":  "dns_security_workflow_fixed.json",
                         "Size":  40331,
                         "LastModified":  "\/Date(1755446103324)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\dns_reports\\setup\\get_dns_cache.ps1",
                         "RelativePath":  "data\\dns_reports\\setup\\get_dns_cache.ps1",
                         "Directory":  "setup",
                         "Name":  "get_dns_cache.ps1",
                         "Size":  2910,
                         "LastModified":  "\/Date(1755462725395)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\dns_reports\\setup\\dns_domain_history.csv",
                         "RelativePath":  "data\\dns_reports\\setup\\dns_domain_history.csv",
                         "Directory":  "setup",
                         "Name":  "dns_domain_history.csv",
                         "Size":  2295,
                         "LastModified":  "\/Date(1755462545121)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\dns_reports\\setup\\dns_domain_history_format.json",
                         "RelativePath":  "data\\dns_reports\\setup\\dns_domain_history_format.json",
                         "Directory":  "setup",
                         "Name":  "dns_domain_history_format.json",
                         "Size":  4110,
                         "LastModified":  "\/Date(1755445991786)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\dns_reports\\setup\\dns_domain_history_schema.sql",
                         "RelativePath":  "data\\dns_reports\\setup\\dns_domain_history_schema.sql",
                         "Directory":  "setup",
                         "Name":  "dns_domain_history_schema.sql",
                         "Size":  4035,
                         "LastModified":  "\/Date(1755446022285)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\data\\dns_reports\\setup\\DOMAIN_HISTORY_GUIDE.md",
                         "RelativePath":  "data\\dns_reports\\setup\\DOMAIN_HISTORY_GUIDE.md",
                         "Directory":  "setup",
                         "Name":  "DOMAIN_HISTORY_GUIDE.md",
                         "Size":  5205,
                         "LastModified":  "\/Date(1755454438099)\/"
                     },
                     {
                         "FullPath":  "C:\\Users\\<USER>\\source\\Cursor_Workspaces\\N8N_Builder\\Documentation\\DNS_Security_Monitor_README.md",
                         "RelativePath":  "Documentation\\DNS_Security_Monitor_README.md",
                         "Directory":  "Documentation",
                         "Name":  "DNS_Security_Monitor_README.md",
                         "Size":  8210,
                         "LastModified":  "\/Date(1755454395789)\/"
                     }
                 ],
    "Timestamp":  "2025-08-17T16:42:04",
    "PathReferences":  [
                           {
                               "SourceFile":  "Scripts\\analyze_dns_project_files.ps1",
                               "ReferencedPath":  " -ForegroundColor Gray\n        \n        try {\n            $content = Get-Content $file.FullPath -Raw -ErrorAction SilentlyContinue\n            if ($content) {\n                # Look for path references\n                $pathMatches = @()\n                \n                # PowerShell script references\n                $pathMatches += [regex]::Matches($content, \u0027Scripts\\\\[^",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\analyze_dns_project_files.ps1",
                               "ReferencedPath":  "\u0027) }\n\n                # Relative path references\n                $pathMatches += [regex]::Matches($content, \u0027\\.\\\\\\w+\\\\[^\\s\\)]+\u0027) | ForEach-Object { $_.Value }\n                \n                foreach ($match in $pathMatches | Sort-Object -Unique) {\n                    if ($match -and $match.Length -gt 3) {\n                        $pathReferences += [PSCustomObject]@{\n                            SourceFile = $file.RelativePath\n                            ReferencedPath = $match\n                            Type = if ($match -match ",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "Scripts\\analyze_dns_project_files.ps1",
                               "ReferencedPath":  "\\.(ps1|bat|md|json)$",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "Scripts\\analyze_dns_project_files.ps1",
                               "ReferencedPath":  "\\s\\)]+\u0027) | ForEach-Object { $_.Value }\n                $pathMatches += [regex]::Matches($content, \u0027Documentation\\\\[^",
                               "Type":  "Documentation"
                           },
                           {
                               "SourceFile":  "Scripts\\analyze_dns_project_files.ps1",
                               "ReferencedPath":  "]*\\\\[^",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "Scripts\\analyze_dns_project_files.ps1",
                               "ReferencedPath":  "^Scripts\\\\",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\analyze_dns_project_files.ps1",
                               "ReferencedPath":  "1. Move DNS-related scripts from Scripts\\ to data\\dns_reports\\setup\\",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\analyze_dns_project_files.ps1",
                               "ReferencedPath":  "Analysis saved to: Scripts\\dns_project_analysis.json",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\analyze_dns_project_files.ps1",
                               "ReferencedPath":  "data\\\\",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "Scripts\\analyze_dns_project_files.ps1",
                               "ReferencedPath":  "data\\\\[^",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "Scripts\\analyze_dns_project_files.ps1",
                               "ReferencedPath":  "data\\dns_reports",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "Scripts\\analyze_dns_project_files.ps1",
                               "ReferencedPath":  "data\\dns_reports\\setup\\",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "Scripts\\analyze_dns_project_files.ps1",
                               "ReferencedPath":  "Documentation\\\\",
                               "Type":  "Documentation"
                           },
                           {
                               "SourceFile":  "Scripts\\analyze_dns_project_files.ps1",
                               "ReferencedPath":  "Documentation\\\\[^",
                               "Type":  "Documentation"
                           },
                           {
                               "SourceFile":  "Scripts\\analyze_dns_project_files.ps1",
                               "ReferencedPath":  "Scripts\\\\",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\analyze_dns_project_files.ps1",
                               "ReferencedPath":  "Scripts\\\\.*dns",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\analyze_dns_project_files.ps1",
                               "ReferencedPath":  "Scripts\\\\.*domain",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\analyze_dns_project_files.ps1",
                               "ReferencedPath":  "Scripts\\\\[^",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\analyze_dns_project_files.ps1",
                               "ReferencedPath":  "Scripts\\dns_project_analysis.json",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\save_dns_report.ps1",
                               "ReferencedPath":  "C:\\DNS_Security_Reports",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "Scripts\\save_dns_report.ps1",
                               "ReferencedPath":  "ðŸŒ To view the report, open: file:///$($FilePath.Replace(\u0027\\\u0027, \u0027/\u0027))",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "Scripts\\save_dns_report.ps1",
                               "ReferencedPath":  "file:///$($FilePath.Replace(\u0027\\\u0027, \u0027/\u0027))",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_automation.ps1",
                               "ReferencedPath":  "   Remove:  .\\Scripts\\setup_dns_automation.ps1 -Remove",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_automation.ps1",
                               "ReferencedPath":  "   Status:  .\\Scripts\\setup_dns_automation.ps1 -Status",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_automation.ps1",
                               "ReferencedPath":  ".\\Scripts\\setup_dns_automation.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_automation.ps1",
                               "ReferencedPath":  "data\\dns_reports\\dns_cache_output.txt",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_automation.ps1",
                               "ReferencedPath":  "Scripts\\setup_dns_automation.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_monitoring.ps1",
                               "ReferencedPath":  "   - Run Scripts\\get_dns_cache.ps1 manually",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_monitoring.ps1",
                               "ReferencedPath":  "   .\\Scripts\\get_dns_cache.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_monitoring.ps1",
                               "ReferencedPath":  "   Check status: .\\\\Scripts\\\\setup_dns_automation.ps1 -Status",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_monitoring.ps1",
                               "ReferencedPath":  "   data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_monitoring.ps1",
                               "ReferencedPath":  "   Expected files: Scripts\\get_dns_cache.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_monitoring.ps1",
                               "ReferencedPath":  ".\\Scripts\\get_dns_cache.ps1\"",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_monitoring.ps1",
                               "ReferencedPath":  "3. Reports will be saved to: data\\dns_reports\\",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_monitoring.ps1",
                               "ReferencedPath":  "4. View dashboard: data\\dns_reports\\index.html",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_monitoring.ps1",
                               "ReferencedPath":  "data\\dns_reports",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_monitoring.ps1",
                               "ReferencedPath":  "data\\dns_reports\\",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_monitoring.ps1",
                               "ReferencedPath":  "data\\dns_reports\\index.html",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_monitoring.ps1",
                               "ReferencedPath":  "data\\dns_reports\\setup",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_monitoring.ps1",
                               "ReferencedPath":  "data\\dns_reports\\setup\\",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_monitoring.ps1",
                               "ReferencedPath":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_monitoring.ps1",
                               "ReferencedPath":  "Scripts\\\\setup_dns_automation.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_monitoring.ps1",
                               "ReferencedPath":  "Scripts\\dns_cache_loop.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_monitoring.ps1",
                               "ReferencedPath":  "Scripts\\get_dns_cache.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_monitoring.ps1",
                               "ReferencedPath":  "Scripts\\setup_dns_automation.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_monitoring.ps1",
                               "ReferencedPath":  "SUCCESS: Created: data\\dns_reports\\",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "Scripts\\setup_dns_monitoring.ps1",
                               "ReferencedPath":  "SUCCESS: Created: data\\dns_reports\\setup\\",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "Scripts\\query_domain_history.ps1",
                               "ReferencedPath":  "  .\\Scripts\\query_domain_history.ps1 -Query export -RiskLevel HIGH",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\query_domain_history.ps1",
                               "ReferencedPath":  "  .\\Scripts\\query_domain_history.ps1 -Query recent -Days 7",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\query_domain_history.ps1",
                               "ReferencedPath":  "  .\\Scripts\\query_domain_history.ps1 -Query search -Domain \u0027google\u0027",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\query_domain_history.ps1",
                               "ReferencedPath":  "  .\\Scripts\\query_domain_history.ps1 -Query summary",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\query_domain_history.ps1",
                               "ReferencedPath":  ".\\Scripts\\query_domain_history.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\query_domain_history.ps1",
                               "ReferencedPath":  "data\\dns_reports\\setup\\dns_domain_history.csv",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "Scripts\\query_domain_history.ps1",
                               "ReferencedPath":  "Scripts\\query_domain_history.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Scripts\\update_domain_history.ps1",
                               "ReferencedPath":  " -ForegroundColor White\n    }\n    \n    # Extract domains from DNS cache\n    $domains = @()\n    $domainPattern = \u0027Record Name[^:]*:\\s*([^\\s\\r\\n]+)\u0027\n    $matches = [regex]::Matches($dnsContent, $domainPattern)\n    \n    foreach ($match in $matches) {\n        $domain = $match.Groups[1].Value.Trim().ToLower()\n        if ($domain -and $domain.Contains(\u0027.\u0027) -and -not $domain.Contains(\u0027localhost\u0027) -and -not ($domain -match \u0027^\\d+\\.\\d+\\.\\d+\\.\\d+$\u0027)) {\n            $domains += $domain\n        }\n    }\n    \n    $uniqueDomains = $domains | Sort-Object | Get-Unique\n    if ($Verbose) {\n        Write-Host ",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "Scripts\\update_domain_history.ps1",
                               "ReferencedPath":  "data\\dns_reports\\setup\\dns_cache_output.txt",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "Scripts\\update_domain_history.ps1",
                               "ReferencedPath":  "data\\dns_reports\\setup\\dns_domain_history.csv",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\collect_dns_cache.bat",
                               "ReferencedPath":  "%~dp0..\\..\\..\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\collect_dns_cache.bat",
                               "ReferencedPath":  "data\\dns_reports\\setup\\dns_cache_output.txt",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\collect_dns_cache.bat",
                               "ReferencedPath":  "Scripts\\get_dns_cache.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\collect_dns_cache.ps1",
                               "ReferencedPath":  "Cannot find Scripts\\get_dns_cache.ps1 in $projectRoot",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\collect_dns_cache.ps1",
                               "ReferencedPath":  "Scripts\\get_dns_cache.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_cache_metadata.json",
                               "ReferencedPath":  "data\\\\dns_reports\\\\setup\\\\dns_cache_output.txt",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  " content=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  " lines which contain domain names\\n  if (line.includes(\u0027Record Name\u0027)) {\\n    const parts = line.split(\u0027:\u0027);\\n    if (parts.length \u003e 1) {\\n      const domain = parts[1].trim();\\n      // Filter out localhost, empty, and IP addresses\\n      if (domain \u0026\u0026 \\n          !domain.includes(\u0027localhost\u0027) \u0026\u0026 \\n          !domain.includes(\u0027127.0.0.1\u0027) \u0026\u0026\\n          !domain.match(/^\\\\d+\\\\.\\\\d+\\\\.\\\\d+\\\\.\\\\d+$/) \u0026\u0026\\n          domain.includes(\u0027.\u0027)) {\\n        domains.add(domain.toLowerCase());\\n      }\\n    }\\n  }\\n}\\n\\n// Convert to array and return as separate items\\nconst domainList = Array.from(domains);\\nconsole.log(`Found ${domainList.length} unique domains:`, domainList);\\n\\nreturn domainList.map(domain =\u003e ({ \\n  domain,\\n  timestamp: new Date().toISOString()\\n}));",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  ".\\n\\n---",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  ".\\n\\nMock",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "// Analyze pattern-based security data and assess risk\\nconst items = $input.all();\\nconst analysis = [];\\nconst highRiskDomains = [];\\n\\nconsole.log(`Processing ${items.length} domain security analyses`);\\n\\nfor (const item of items) {\\n  // The security check already did the analysis, just extract the results\\n  const securityResult = item.json;\\n  \\n  const domainAnalysis = {\\n    domain: securityResult.domain || securityResult.originalDomain,\\n    riskLevel: securityResult.riskLevel,\\n    threats: securityResult.threats || [],\\n    details: securityResult.details,\\n    positives: securityResult.positives || securityResult.suspiciousScore || 0,\\n    total: securityResult.total || 10,\\n    timestamp: new Date().toISOString(),\\n    source: \u0027Pattern Analysis\u0027,\\n    analysisMethod: securityResult.analysisMethod || \u0027pattern_based\u0027,\\n    engines_flagged: securityResult.engines_flagged || [],\\n    clean_engines: securityResult.clean_engines || []\\n  };\\n  \\n  analysis.push(domainAnalysis);\\n  \\n  if (domainAnalysis.riskLevel === \u0027HIGH\u0027) {\\n    highRiskDomains.push(domainAnalysis);\\n  }\\n  \\n  console.log(`Domain: ${domainAnalysis.domain} - Risk: ${domainAnalysis.riskLevel} - Threats: ${domainAnalysis.threats.length}`);\\n}\\n\\nconsole.log(`Analysis complete: ${analysis.length} domains processed, ${highRiskDomains.length} high-risk domains found`);\\n\\n// Log high-risk domains for debugging\\nif (highRiskDomains.length \u003e 0) {\\n  console.log(\u0027HIGH RISK DOMAINS DETECTED:\u0027);\\n  highRiskDomains.forEach(domain =\u003e {\\n    console.log(`  - ${domain.domain}: ${domain.details}`);\\n    console.log(`    Threats: ${domain.threats.join(\u0027, \u0027)}`);\\n  });\\n}\\n\\nreturn [{\\n  analysis,\\n  highRiskDomains,\\n  totalDomains: analysis.length,\\n  highRiskCount: highRiskDomains.length,\\n  hasHighRisk: highRiskDomains.length \u003e 0,\\n  reportTimestamp: new Date().toISOString()\\n}];",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "// Generate comprehensive HTML security report\\nconst data = $input.first().json;\\nconst { analysis, highRiskDomains, totalDomains, highRiskCount, reportTimestamp } = data;\\n\\nconsole.log(\u0027=== GENERATING HTML REPORT ===\u0027);\\nconsole.log(`Total domains: ${totalDomains}`);\\nconsole.log(`High risk domains: ${highRiskCount}`);\\n\\n// Generate report filename\\nconst reportDate = new Date().toISOString().split(\u0027T\u0027)[0];\\nconst fileName = `dns_security_report_${reportDate}.html`;\\n\\n// Count domains by risk level\\nconst riskCounts = {\\n  HIGH: analysis.filter(d =\u003e d.riskLevel === \u0027HIGH\u0027).length,\\n  MEDIUM: analysis.filter(d =\u003e d.riskLevel === \u0027MEDIUM\u0027).length,\\n  LOW: analysis.filter(d =\u003e d.riskLevel === \u0027LOW\u0027).length\\n};\\n\\n// Generate HTML report\\nconst htmlReport = `\u003c!DOCTYPE html\u003e\\n\u003chtml lang=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "// Generate error report for system failures\\nconst data = $input.first().json;\\n\\nconsole.log(\u0027=== GENERATING SYSTEM ERROR REPORT ===\u0027);\\nconsole.log(\u0027Error Type:\u0027, data.errorType);\\nconsole.log(\u0027Error Message:\u0027, data.errorMessage);\\n\\nconst errorDate = new Date().toISOString().split(\u0027T\u0027)[0];\\nconst fileName = `dns_system_error_report_${errorDate}.html`;\\n\\n// Generate error report HTML\\nconst errorReport = `\u003c!DOCTYPE html\u003e\\n\u003chtml lang=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "// Parse DNS cache output to extract domain names\\nconst dnsOutput = $input.first().json.stdout || \u0027\u0027;\\nconst domains = new Set();\\n\\nconsole.log(\u0027=== PARSING DNS DOMAINS ===\u0027);\\nconsole.log(\u0027DNS output length:\u0027, dnsOutput.length);\\n\\n// Split output into lines and process\\nconst lines = dnsOutput.split(\u0027\\\\n\u0027);\\nfor (const line of lines) {\\n  // Look for \\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "// Pattern-based security analysis for domains\\nconst domain = $input.first().json.domain;\\nconsole.log(`Analyzing domain: ${domain}`);\\n\\n// Simple domain analysis based on patterns and characteristics\\nlet riskLevel = \u0027LOW\u0027;\\nlet threats = [];\\nlet details = \u0027Domain analysis completed\u0027;\\nlet suspiciousScore = 0;\\n\\n// Check for suspicious domain patterns\\nconst highRiskPatterns = [\\n  /suspicious/i, /malware/i, /phish/i, /hack/i, /virus/i, \\n  /spam/i, /scam/i, /fake/i, /fraud/i, /trojan/i\\n];\\n\\nconst mediumRiskPatterns = [\\n  /test/i, /temp/i, /demo/i, /sample/i, /example/i,\\n  /\\\\d{1,3}-\\\\d{1,3}-\\\\d{1,3}-\\\\d{1,3}/i, // IP-like patterns\\n  /[0-9]{8,}/i // Long number sequences\\n];\\n\\n// Check domain length and structure\\nconst domainParts = domain.split(\u0027.\u0027);\\nconst domainName = domainParts[0];\\n\\n// High-risk pattern checks\\nfor (const pattern of highRiskPatterns) {\\n  if (pattern.test(domain)) {\\n    riskLevel = \u0027HIGH\u0027;\\n    threats.push(\u0027Suspicious domain name pattern detected\u0027);\\n    suspiciousScore += 3;\\n    break;\\n  }\\n}\\n\\n// Medium-risk pattern checks (only if not already high risk)\\nif (riskLevel !== \u0027HIGH\u0027) {\\n  for (const pattern of mediumRiskPatterns) {\\n    if (pattern.test(domain)) {\\n      riskLevel = \u0027MEDIUM\u0027;\\n      threats.push(\u0027Potentially suspicious domain characteristics\u0027);\\n      suspiciousScore += 1;\\n      break;\\n    }\\n  }\\n}\\n\\n// Check for very long domain names (potential DGA)\\nif (domainName.length \u003e 20) {\\n  if (riskLevel === \u0027LOW\u0027) riskLevel = \u0027MEDIUM\u0027;\\n  threats.push(\u0027Unusually long domain name\u0027);\\n  suspiciousScore += 1;\\n}\\n\\n// Check for excessive subdomains\\nif (domainParts.length \u003e 4) {\\n  if (riskLevel === \u0027LOW\u0027) riskLevel = \u0027MEDIUM\u0027;\\n  threats.push(\u0027Multiple subdomains detected\u0027);\\n  suspiciousScore += 1;\\n}\\n\\n// Check for known good domains (whitelist)\\nconst knownGoodDomains = [\\n  \u0027google.com\u0027, \u0027microsoft.com\u0027, \u0027github.com\u0027, \u0027stackoverflow.com\u0027,\\n  \u0027amazon.com\u0027, \u0027facebook.com\u0027, \u0027twitter.com\u0027, \u0027linkedin.com\u0027,\\n  \u0027youtube.com\u0027, \u0027wikipedia.org\u0027, \u0027apple.com\u0027, \u0027adobe.com\u0027\\n];\\n\\nconst isKnownGood = knownGoodDomains.some(goodDomain =\u003e \\n  domain.toLowerCase().includes(goodDomain.toLowerCase())\\n);\\n\\nif (isKnownGood \u0026\u0026 riskLevel !== \u0027HIGH\u0027) {\\n  riskLevel = \u0027LOW\u0027;\\n  threats = []; // Clear any medium-risk flags for known good domains\\n  details = \u0027Known legitimate domain\u0027;\\n  suspiciousScore = 0;\\n}\\n\\n// Set final details\\nif (riskLevel === \u0027HIGH\u0027) {\\n  details = `High-risk domain detected (score: ${suspiciousScore})`;\\n} else if (riskLevel === \u0027MEDIUM\u0027) {\\n  details = `Potentially suspicious domain (score: ${suspiciousScore})`;\\n} else {\\n  details = \u0027Domain appears legitimate\u0027;\\n}\\n\\n// Add some realistic-looking metadata\\nconst analysisResult = {\\n  domain: domain,\\n  riskLevel: riskLevel,\\n  threats: threats,\\n  details: details,\\n  suspiciousScore: suspiciousScore,\\n  analysisMethod: \u0027pattern_based\u0027,\\n  timestamp: new Date().toISOString(),\\n  // Simulate security engine results\\n  positives: suspiciousScore,\\n  total: 10, // Simulated total engines\\n  engines_flagged: threats.length \u003e 0 ? [\u0027Pattern Analysis\u0027, \u0027Domain Structure Check\u0027] : [],\\n  clean_engines: threats.length === 0 ? [\u0027Pattern Analysis\u0027, \u0027Domain Structure Check\u0027, \u0027Whitelist Check\u0027] : [\u0027Whitelist Check\u0027]\\n};\\n\\nconsole.log(`Domain analysis result:`, JSON.stringify(analysisResult, null, 2));\\n\\nreturn [{\\n  json: {\\n    ...analysisResult,\\n    // Keep original domain for next node\\n    originalDomain: domain\\n  }\\n}];",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "// Prepare alert file data for N8N Write Binary File node\\nconst data = $input.first().json;\\nconst alertFileName = data.alertFileName;\\nconst alertContent = data.alertContent;\\n\\nconsole.log(\u0027=== PREPARING ALERT FILE FOR SAVE ===\u0027);\\nconsole.log(`ðŸš¨ HIGH RISK DOMAINS DETECTED! ðŸš¨`);\\nconsole.log(`Alert file: ${alertFileName}`);\\nconsole.log(`Content length: ${alertContent ? alertContent.length : \u0027N/A\u0027}`);\\n\\n// Convert alert content to binary data for N8N Write Binary File node\\nconst binaryData = {\\n  data: Buffer.from(alertContent, \u0027utf8\u0027).toString(\u0027base64\u0027),\\n  mimeType: \u0027text/plain\u0027,\\n  fileName: alertFileName,\\n  fileExtension: \u0027txt\u0027\\n};\\n\\nconsole.log(\u0027âœ… Alert content prepared for file writing\u0027);\\nconsole.log(`   Binary data size: ${binaryData.data.length} bytes (base64)`);\\nconsole.log(`   Target: /home/<USER>/shared/dns_reports/${alertFileName}`);\\nconsole.log(`   Host path: ./data/dns_reports/${alertFileName}`);\\n\\nconsole.log(\u0027\\\\nðŸ“„ ALERT CONTENT:\u0027);\\nconsole.log(\u0027=\u0027 * 50);\\nconsole.log(alertContent);\\nconsole.log(\u0027=\u0027 * 50);\\n\\nreturn [{\\n  json: {\\n    ...data,\\n    alertFileName: alertFileName,\\n    binaryData: binaryData\\n  },\\n  binary: {\\n    data: binaryData\\n  }\\n}];",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "// Prepare detailed alert content for high-risk domains\\nconst data = $input.first().json;\\nconst { reportData } = data;\\nconst { highRiskDomains, totalDomains, reportTimestamp } = reportData;\\n\\nconsole.log(\u0027=== PREPARING SECURITY ALERT ===\u0027);\\nconsole.log(`ðŸš¨ HIGH RISK DOMAINS DETECTED: ${highRiskDomains.length}`);\\n\\n// Generate alert filename with timestamp\\nconst alertTimestamp = Date.now();\\nconst alertDate = new Date().toISOString().split(\u0027T\u0027)[0];\\nconst alertFileName = `DNS_SECURITY_ALERT_${alertDate}_${alertTimestamp}.txt`;\\n\\n// Create detailed alert content\\nconst alertContent = `ðŸš¨ DNS SECURITY ALERT ðŸš¨\\nTime: ${new Date().toLocaleString()}\\nHigh-Risk Domains Found: ${highRiskDomains.length}\\nTotal Domains Scanned: ${totalDomains}\\n\\nIMMEDIATE ACTION REQUIRED:\\n\\n${highRiskDomains.map(domain =\u003e `âš ï¸  DOMAIN: ${domain.domain}\\n   RISK: ${domain.riskLevel}\\n   DETAILS: ${domain.details}\\n   THREATS: ${domain.threats.join(\u0027, \u0027)}\\n   SCORE: ${domain.positives}/${domain.total}`).join(\u0027\\\\n\\\\n\u0027)}\\n\\nFull HTML Report: ${data.fileName}\\nNext scan in 60 minutes.\\n\\n--- DNS Security Monitor ---`;\\n\\nconsole.log(\u0027Alert content prepared\u0027);\\nconsole.log(`Alert file: ${alertFileName}`);\\nconsole.log(`Content length: ${alertContent.length} characters`);\\n\\n// Create notification text for Windows notification\\nconst notificationText = `${highRiskDomains.length} high-risk domains detected: ${highRiskDomains.map(d =\u003e d.domain).join(\u0027, \u0027)}. Check alert file: ${alertFileName}`;\\n\\nreturn [{\\n  json: {\\n    ...data,\\n    alertFileName,\\n    alertContent,\\n    notificationText,\\n    timestamp: new Date().toLocaleString(),\\n    highRiskCount: highRiskDomains.length,\\n    totalDomains\\n  }\\n}];",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "// Prepare HTML report data for N8N Write Binary File node\\nconst data = $input.first().json;\\nconst fileName = data.fileName;\\nconst htmlContent = data.htmlContent || data.htmlReport;\\n\\nconsole.log(\u0027=== PREPARING HTML REPORT FOR FILE SAVE ===\u0027);\\nconsole.log(`File name: ${fileName}`);\\nconsole.log(`Content length: ${htmlContent ? htmlContent.length : \u0027N/A\u0027} characters`);\\nconsole.log(`High risk domains: ${data.highRiskCount}`);\\nconsole.log(`Total domains: ${data.totalDomains}`);\\n\\n// Convert HTML string to binary data for N8N Write Binary File node\\nconst binaryData = {\\n  data: Buffer.from(htmlContent, \u0027utf8\u0027).toString(\u0027base64\u0027),\\n  mimeType: \u0027text/html\u0027,\\n  fileName: fileName,\\n  fileExtension: \u0027html\u0027\\n};\\n\\nconsole.log(\u0027âœ… HTML content prepared for file writing\u0027);\\nconsole.log(`   Binary data size: ${binaryData.data.length} bytes (base64)`);\\nconsole.log(`   MIME type: ${binaryData.mimeType}`);\\nconsole.log(`   Target: /home/<USER>/shared/dns_reports/${fileName}`);\\nconsole.log(`   Host path: ./data/dns_reports/${fileName}`);\\n\\nreturn [{\\n  json: {\\n    ...data,\\n    fileName: fileName,\\n    binaryData: binaryData\\n  },\\n  binary: {\\n    data: binaryData\\n  }\\n}];",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "// Process DNS cache data from file or provide fallback\\nconst input = $input.first();\\n\\nconsole.log(\u0027=== PROCESSING DNS CACHE DATA ===\u0027);\\n\\nlet dnsOutput = \u0027\u0027;\\nlet source = \u0027unknown\u0027;\\nlet status = \u0027success\u0027;\\n\\n// Check if we have binary data from the file read\\nif (input.binary \u0026\u0026 input.binary.data) {\\n  try {\\n    // Convert binary data to text\\n    const binaryData = input.binary.data;\\n    dnsOutput = Buffer.from(binaryData.data, \u0027base64\u0027).toString(\u0027utf8\u0027);\\n    source = \u0027windows_dns_cache_file\u0027;\\n    console.log(`SUCCESS: DNS cache file read successfully`);\\n    console.log(`   Content length: ${dnsOutput.length} characters`);\\n    console.log(`   Lines: ${dnsOutput.split(\u0027\\\\n\u0027).length}`);\\n    \\n    // Show sample domains found\\n    const domainMatches = dnsOutput.match(/Record Name[^:]*:[^\\\\n]*/g);\\n    if (domainMatches \u0026\u0026 domainMatches.length \u003e 0) {\\n      console.log(`   Domains found: ${domainMatches.length}`);\\n      console.log(`   Sample: ${domainMatches.slice(0, 3).map(m =\u003e m.split(\u0027:\u0027)[1]?.trim()).join(\u0027, \u0027)}`);\\n    }\\n    \\n  } catch (error) {\\n    console.log(\u0027ERROR: Error processing DNS file data:\u0027, error.message);\\n    dnsOutput = \u0027\u0027;\\n    status = \u0027file_read_error\u0027;\\n  }\\n}\\n\\n// If no valid DNS data, provide fallback with instructions\\nif (!dnsOutput || dnsOutput.length \u003c 100) {\\n  console.log(\u0027WARNING: No valid DNS cache data found. Using fallback.\u0027);\\n  console.log(\u0027   Batch file may have failed to collect DNS data\u0027);\\n  console.log(\u0027   Check batch file execution logs above\u0027);\\n  \\n  source = \u0027fallback_mock_data\u0027;\\n  status = \u0027using_fallback\u0027;\\n  \\n  dnsOutput = `DNS Cache Collection Failed - Using Mock Data\\n\\nBatch file execution may have failed.\\nCheck N8N console logs for batch file output.\\n\\nMock DNS data for testing:\\n\\n    example.com\\n    ----------------------------------------\\n    Record Name . . . . . : example.com\\n    Record Type . . . . . : 1\\n    Time To Live  . . . . : 300\\n    Data Length . . . . . : 4\\n    Section . . . . . . . : Answer\\n    A (Host) Record . . . : *************\\n\\n    google.com\\n    ----------------------------------------\\n    Record Name . . . . . : google.com\\n    Record Type . . . . . : 1\\n    Time To Live  . . . . : 300\\n    Data Length . . . . . : 4\\n    Section . . . . . . . : Answer\\n    A (Host) Record . . . : **************\\n\\n    github.com\\n    ----------------------------------------\\n    Record Name . . . . . : github.com\\n    Record Type . . . . . : 1\\n    Time To Live  . . . . : 300\\n    Data Length . . . . . : 4\\n    Section . . . . . . . : Answer\\n    A (Host) Record . . . : ************\\n\\n    suspicious-test-domain.com\\n    ----------------------------------------\\n    Record Name . . . . . : suspicious-test-domain.com\\n    Record Type . . . . . : 1\\n    Time To Live  . . . . : 300\\n    Data Length . . . . . : 4\\n    Section . . . . . . . : Answer\\n    A (Host) Record . . . : *************`;\\n}\\n\\nreturn [{\\n  json: {\\n    stdout: dnsOutput,\\n    stderr: status === \u0027success\u0027 ? \u0027\u0027 : \u0027Using fallback data - batch file may have failed\u0027,\\n    exitCode: status === \u0027success\u0027 ? 0 : 1,\\n    command: \u0027dns_cache_reader\u0027,\\n    source: source,\\n    status: status,\\n    timestamp: new Date().toISOString()\\n  }\\n}];",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "// Update domain history tracking in CSV file\\nconst domains = $input.all();\\nconst currentTime = new Date().toISOString();\\n\\nconsole.log(\u0027=== UPDATING DOMAIN HISTORY ===\u0027);\\nconsole.log(`Processing ${domains.length} domains for history tracking`);\\n\\n// Note: This is a placeholder for domain history tracking\\n// In a future version, this would:\\n// 1. Read existing domain history CSV file\\n// 2. Update last_seen dates for existing domains\\n// 3. Add new domains with first_seen dates\\n// 4. Increment occurrence counts\\n// 5. Write updated CSV back to file\\n\\nconst historyFilePath = \u0027/home/<USER>/shared/dns_reports/setup/dns_domain_history.csv\u0027;\\nconsole.log(\u0027Domain history file:\u0027, historyFilePath);\\n\\n// Process each domain for history tracking preparation\\nconst historyUpdates = [];\\n\\nfor (const domainItem of domains) {\\n  const domain = domainItem.json.domain.toLowerCase();\\n  const timestamp = domainItem.json.timestamp;\\n  \\n  // Prepare history record structure (for future implementation)\\n  const historyRecord = {\\n    domain_name: domain,\\n    ip_address: \u0027\u0027, // Would be extracted from DNS data\\n    first_seen: timestamp,\\n    last_seen: timestamp,\\n    occurrence_count: 1,\\n    current_risk_level: \u0027LOW\u0027, // Will be updated after security analysis\\n    highest_risk_level: \u0027LOW\u0027,\\n    threat_categories: \u0027\u0027,\\n    dns_record_type: \u0027A\u0027,\\n    created_date: timestamp,\\n    modified_date: timestamp\\n  };\\n  \\n  historyUpdates.push(historyRecord);\\n  console.log(`Prepared history tracking for: ${domain}`);\\n}\\n\\nconsole.log(`Domain history tracking prepared for ${historyUpdates.length} domains`);\\nconsole.log(\u0027Note: CSV file updates will be implemented via PowerShell script integration\u0027);\\n\\n// Pass through all domains for continued processing\\nreturn domains.map(item =\u003e ({\\n  json: {\\n    ...item.json,\\n    historyTracked: true,\\n    historyTimestamp: currentTime\\n  }\\n}));",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "// Validate DNS cache file freshness and stop if stale\\nconst input = $input.first().json;\\nconst fileCheckOutput = input.stdout?.trim();\\n\\nconsole.log(\u0027=== DNS CACHE FILE FRESHNESS CHECK ===\u0027);\\nconsole.log(\u0027File check output:\u0027, fileCheckOutput);\\n\\nconst currentTime = Math.floor(Date.now() / 1000); // Unix timestamp\\nconst maxAgeMinutes = 65; // File must be newer than 65 minutes (Windows task runs every 60 min)\\nconst maxAgeSeconds = maxAgeMinutes * 60;\\n\\nlet fileStatus = {\\n  exists: false,\\n  ageMinutes: 0,\\n  isValid: false,\\n  error: null\\n};\\n\\nif (fileCheckOutput === \u0027FILE_NOT_FOUND\u0027 || !fileCheckOutput || fileCheckOutput.includes(\u0027No such file\u0027)) {\\n  fileStatus.error = \u0027DNS cache file does not exist\u0027;\\n  console.log(\u0027âŒ CRITICAL ERROR: DNS cache file not found\u0027);\\n  console.log(\u0027   Expected: /home/<USER>/shared/dns_reports/setup/dns_cache_output.txt\u0027);\\n  console.log(\u0027   This indicates Windows automation is not working\u0027);\\n  \\n} else {\\n  try {\\n    const fileTimestamp = parseInt(fileCheckOutput);\\n    const fileAge = currentTime - fileTimestamp;\\n    fileStatus.ageMinutes = Math.floor(fileAge / 60);\\n    fileStatus.exists = true;\\n    \\n    console.log(`File timestamp: ${fileTimestamp} (${new Date(fileTimestamp * 1000).toISOString()})`);\\n    console.log(`Current time: ${currentTime} (${new Date(currentTime * 1000).toISOString()})`);\\n    console.log(`File age: ${fileStatus.ageMinutes} minutes`);\\n    \\n    if (fileAge \u003c= maxAgeSeconds) {\\n      fileStatus.isValid = true;\\n      console.log(\u0027âœ… DNS cache file is fresh and valid\u0027);\\n    } else {\\n      fileStatus.error = `DNS cache file is too old (${fileStatus.ageMinutes} minutes, max ${maxAgeMinutes})`;\\n      console.log(`âŒ CRITICAL ERROR: DNS cache file is stale`);\\n      console.log(`   File age: ${fileStatus.ageMinutes} minutes (maximum allowed: ${maxAgeMinutes})`);\\n      console.log(`   This indicates Windows automation has stopped working`);\\n    }\\n  } catch (error) {\\n    fileStatus.error = `Error parsing file timestamp: ${error.message}`;\\n    console.log(\u0027âŒ CRITICAL ERROR: Cannot parse file timestamp\u0027);\\n    console.log(\u0027   Raw output:\u0027, fileCheckOutput);\\n  }\\n}\\n\\nif (!fileStatus.isValid) {\\n  console.log(\u0027\\\\nðŸš¨ AUTOMATION FAILURE DETECTED ðŸš¨\u0027);\\n  console.log(\u0027The DNS Security Monitor automation is not working properly.\u0027);\\n  console.log(\u0027\\\\nRequired Actions:\u0027);\\n  console.log(\u00271. Check Windows Task Scheduler: .\\\\\\\\Scripts\\\\\\\\setup_dns_automation.ps1 -Status\u0027);\\n  console.log(\u00272. Manually run: .\\\\\\\\Scripts\\\\\\\\get_dns_cache.ps1\u0027);\\n  console.log(\u00273. Verify Docker volume mount is working\u0027);\\n  console.log(\u00274. Check PowerShell execution policy\u0027);\\n  console.log(\u0027\\\\nWorkflow will generate ERROR REPORT instead of security analysis.\u0027);\\n  \\n  // Return error status that will trigger error report generation\\n  return [{\\n    json: {\\n      systemError: true,\\n      errorType: \u0027DNS_CACHE_AUTOMATION_FAILURE\u0027,\\n      errorMessage: fileStatus.error,\\n      fileStatus: fileStatus,\\n      troubleshooting: {\\n        checkTaskScheduler: \u0027.\\\\\\\\Scripts\\\\\\\\setup_dns_automation.ps1 -Status\u0027,\\n        manualCollection: \u0027.\\\\\\\\Scripts\\\\\\\\get_dns_cache.ps1\u0027,\\n        setupAutomation: \u0027.\\\\\\\\Scripts\\\\\\\\setup_dns_monitoring.ps1\u0027\\n      },\\n      timestamp: new Date().toISOString()\\n    }\\n  }];\\n}\\n\\n// File is valid, proceed with normal workflow\\nconsole.log(\u0027DNS cache file validation passed - proceeding with analysis\u0027);\\nreturn [{\\n  json: {\\n    systemError: false,\\n    fileStatus: fileStatus,\\n    validationPassed: true,\\n    timestamp: new Date().toISOString()\\n  }\\n}];",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "// Windows notification simulation (child_process not available in N8N Code node)\\nconst data = $input.first().json;\\n\\n// Since we can\u0027t execute PowerShell from N8N Code node, we\u0027ll log the notification details\\nconsole.log(\u0027=== WINDOWS NOTIFICATION SIMULATION ===\u0027);\\nconsole.log(`ðŸš¨ DNS SECURITY ALERT ðŸš¨`);\\nconsole.log(`High-risk domains detected: ${data.highRiskCount}`);\\nconsole.log(`Alert file created: ${data.alertFileName}`);\\nconsole.log(`Notification text: ${data.notificationText}`);\\nconsole.log(`Timestamp: ${data.timestamp}`);\\nconsole.log(\u0027========================================\u0027);\\n\\n// Create a notification record for the logs\\nconst notificationRecord = {\\n  type: \u0027windows_notification_simulation\u0027,\\n  title: \u0027ðŸš¨ DNS Security Alert\u0027,\\n  message: data.notificationText,\\n  alertFile: data.alertFileName,\\n  highRiskCount: data.highRiskCount,\\n  timestamp: data.timestamp,\\n  status: \u0027simulated_in_n8n_logs\u0027\\n};\\n\\nconsole.log(\u0027Notification record:\u0027, JSON.stringify(notificationRecord, null, 2));\\n\\nreturn [{\\n  ...data,\\n  notificationRecord,\\n  notificationStatus: \u0027logged_to_console\u0027\\n}];",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e${data.troubleshooting.checkTaskScheduler}\u003c/div\u003e\\n            \\n            \u003ch4\u003eStep 2: Manual DNS Collection (Immediate Fix)\u003c/h4\u003e\\n            \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e${data.troubleshooting.manualCollection}\u003c/div\u003e\\n            \\n            \u003ch4\u003eStep 3: Restart Automation (If Needed)\u003c/h4\u003e\\n            \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e${data.troubleshooting.setupAutomation}\u003c/div\u003e\\n            \\n            \u003ch4\u003eStep 4: Verify File Creation\u003c/h4\u003e\\n            \u003cp\u003eAfter running the manual collection, verify that this file exists and is recent:\u003c/p\u003e\\n            \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e${domain.riskLevel}\u003c/span\u003e\u003c/td\u003e\\n                    \u003ctd\u003e${domain.positives}/${domain.total}\u003c/td\u003e\\n                    \u003ctd\u003e${domain.details}\u003c/td\u003e\\n                    \u003ctd\u003e${domain.threats.join(\u0027, \u0027) || \u0027None detected\u0027}\u003c/td\u003e\\n                \u003c/tr\u003e\\n                `).join(\u0027\u0027)}\\n            \u003c/tbody\u003e\\n        \u003c/table\u003e\\n        \\n        \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e${riskCounts.HIGH}\u003c/div\u003e\\n            \u003c/div\u003e\\n            \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e${riskCounts.LOW}\u003c/div\u003e\\n            \u003c/div\u003e\\n            \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e${riskCounts.MEDIUM}\u003c/div\u003e\\n            \u003c/div\u003e\\n            \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e${totalDomains}\u003c/div\u003e\\n            \u003c/div\u003e\\n        \u003c/div\u003e\\n        \\n        ${highRiskCount \u003e 0 ? `\\n        \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e\\n                \u003ch3\u003eHigh Risk Domains\u003c/h3\u003e\\n                \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e\\n                \u003ch3\u003eLow Risk Domains\u003c/h3\u003e\\n                \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e\\n                \u003ch3\u003eMedium Risk Domains\u003c/h3\u003e\\n                \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e\\n                \u003ch3\u003eTotal Domains Analyzed\u003c/h3\u003e\\n                \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e\\n            \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e\\n            \u003ch1\u003eðŸ›¡ï¸ DNS Security Monitoring Report\u003c/h1\u003e\\n            \u003cp\u003eGenerated: ${new Date().toLocaleString()}\u003c/p\u003e\\n        \u003c/div\u003e\\n        \\n        \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e\\n            \u003ch1\u003eðŸš¨ DNS Security Monitor - System Error\u003c/h1\u003e\\n            \u003cp\u003eAutomation Failure Detected: ${new Date().toLocaleString()}\u003c/p\u003e\\n        \u003c/div\u003e\\n        \\n        \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e\\n            \u003ch3\u003eâŒ Critical System Error\u003c/h3\u003e\\n            \u003cp\u003e\u003cstrong\u003eError Type:\u003c/strong\u003e ${data.errorType}\u003c/p\u003e\\n            \u003cp\u003e\u003cstrong\u003eError Message:\u003c/strong\u003e ${data.errorMessage}\u003c/p\u003e\\n            \u003cp\u003e\u003cstrong\u003eImpact:\u003c/strong\u003e DNS security monitoring is not functioning. No real DNS analysis can be performed.\u003c/p\u003e\\n        \u003c/div\u003e\\n        \\n        \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e\\n            \u003ch3\u003eâœ… All Clear\u003c/h3\u003e\\n            \u003cp\u003eNo high-risk domains detected in this scan.\u003c/p\u003e\\n        \u003c/div\u003e\\n        `}\\n        \\n        \u003ch2\u003eðŸ“Š Detailed Analysis\u003c/h2\u003e\\n        \u003ctable\u003e\\n            \u003cthead\u003e\\n                \u003ctr\u003e\\n                    \u003cth\u003eDomain\u003c/th\u003e\\n                    \u003cth\u003eRisk Level\u003c/th\u003e\\n                    \u003cth\u003eSecurity Score\u003c/th\u003e\\n                    \u003cth\u003eDetails\u003c/th\u003e\\n                    \u003cth\u003eThreats\u003c/th\u003e\\n                \u003c/tr\u003e\\n            \u003c/thead\u003e\\n            \u003ctbody\u003e\\n                ${analysis.map(domain =\u003e `\\n                \u003ctr\u003e\\n                    \u003ctd\u003e${domain.domain}\u003c/td\u003e\\n                    \u003ctd\u003e\u003cspan class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e\\n            \u003ch3\u003eâš ï¸ Security Alert\u003c/h3\u003e\\n            \u003cp\u003e\u003cstrong\u003e${highRiskCount} high-risk domain(s) detected!\u003c/strong\u003e Immediate review recommended.\u003c/p\u003e\\n        \u003c/div\u003e\\n        ` : `\\n        \u003cdiv style=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e\\n            \u003ch3\u003eðŸ“Š File Status Details\u003c/h3\u003e\\n            \u003cp\u003e\u003cstrong\u003eFile Exists:\u003c/strong\u003e ${data.fileStatus.exists ? \u0027Yes\u0027 : \u0027No\u0027}\u003c/p\u003e\\n            ${data.fileStatus.ageMinutes ? `\u003cp\u003e\u003cstrong\u003eFile Age:\u003c/strong\u003e ${data.fileStatus.ageMinutes} minutes\u003c/p\u003e` : \u0027\u0027}\\n            \u003cp\u003e\u003cstrong\u003eMaximum Allowed Age:\u003c/strong\u003e 65 minutes\u003c/p\u003e\\n            \u003cp\u003e\u003cstrong\u003eExpected Update Frequency:\u003c/strong\u003e Every 55 minutes via Windows Task Scheduler\u003c/p\u003e\\n        \u003c/div\u003e\\n        ` : \u0027\u0027}\\n        \\n        \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e\\n            \u003ch3\u003eðŸ”§ Required Actions\u003c/h3\u003e\\n            \u003cp\u003eThe DNS cache automation system has failed. Please follow these steps to restore functionality:\u003c/p\u003e\\n            \\n            \u003ch4\u003eStep 1: Check Task Scheduler Status\u003c/h4\u003e\\n            \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e\\n            \u003cp\u003e\u003cstrong\u003eDNS Security Monitor - System Error Report\u003c/strong\u003e\u003c/p\u003e\\n            \u003cp\u003eGenerated: ${new Date().toLocaleString()}\u003c/p\u003e\\n            \u003cp\u003eThis report will be replaced with normal security analysis once the automation is restored.\u003c/p\u003e\\n        \u003c/div\u003e\\n    \u003c/div\u003e\\n\u003c/body\u003e\\n\u003c/html\u003e`;\\n\\nconsole.log(`Error report generated: ${fileName}`);\\nconsole.log(`Report size: ${errorReport.length} characters`);\\n\\nreturn [{\\n  json: {\\n    htmlReport: errorReport,\\n    htmlContent: errorReport,\\n    fileName: fileName,\\n    isErrorReport: true,\\n    errorType: data.errorType,\\n    errorMessage: data.errorMessage,\\n    timestamp: new Date().toISOString()\\n  }\\n}];",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e\\n            \u003cp\u003e\u003cstrong\u003eReport generated by N8N DNS Security Monitor\u003c/strong\u003e\u003c/p\u003e\\n            \u003cp\u003eNext scan scheduled in 60 minutes\u003c/p\u003e\\n            \u003cp\u003eReport saved: ${fileName}\u003c/p\u003e\\n        \u003c/div\u003e\\n    \u003c/div\u003e\\n\u003c/body\u003e\\n\u003c/html\u003e`;\\n\\nconsole.log(`HTML report generated: ${fileName}`);\\nconsole.log(`Report size: ${htmlReport.length} characters`);\\n\\nreturn [{\\n  htmlReport,\\n  htmlContent: htmlReport,\\n  fileName,\\n  hasHighRisk: highRiskCount \u003e 0,\\n  highRiskCount,\\n  totalDomains,\\n  reportData: data\\n}];",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e\\n        \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e\\n    \u003cmeta name=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e\\n    \u003ctitle\u003eDNS Security Monitor - System Error\u003c/title\u003e\\n    \u003cstyle\u003e\\n        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }\\n        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\\n        .error-header { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 30px; }\\n        .error-header h1 { margin: 0; font-size: 2.5em; }\\n        .error-header p { margin: 10px 0 0 0; opacity: 0.9; }\\n        .error-details { background: #fff5f5; border: 2px solid #dc3545; padding: 20px; border-radius: 10px; margin: 20px 0; }\\n        .error-details h3 { color: #dc3545; margin-top: 0; }\\n        .troubleshooting { background: #f8f9fa; border-left: 5px solid #007bff; padding: 20px; margin: 20px 0; }\\n        .troubleshooting h3 { color: #007bff; margin-top: 0; }\\n        .command { background: #f1f3f4; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }\\n        .status-info { background: #e8f5e8; border: 1px solid #4caf50; padding: 15px; border-radius: 5px; margin: 20px 0; }\\n        .footer { margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px; text-align: center; color: #666; }\\n    \u003c/style\u003e\\n\u003c/head\u003e\\n\u003cbody\u003e\\n    \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e\\n    \u003ctitle\u003eDNS Security Report - ${new Date().toLocaleDateString()}\u003c/title\u003e\\n    \u003cstyle\u003e\\n        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }\\n        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\\n        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 30px; }\\n        .header h1 { margin: 0; font-size: 2.5em; }\\n        .header p { margin: 10px 0 0 0; opacity: 0.9; }\\n        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }\\n        .summary-card { background: #f8f9fa; border-left: 5px solid #28a745; padding: 20px; border-radius: 5px; }\\n        .summary-card.high-risk { border-left-color: #dc3545; background: #fff5f5; }\\n        .summary-card.medium-risk { border-left-color: #ffc107; background: #fffbf0; }\\n        .summary-card h3 { margin: 0 0 10px 0; color: #333; }\\n        .summary-card .number { font-size: 2em; font-weight: bold; color: #666; }\\n        .high-risk { color: #d32f2f; font-weight: bold; }\\n        .medium-risk { color: #f57c00; font-weight: bold; }\\n        .low-risk { color: #388e3c; font-weight: bold; }\\n        .alert { background: #ffebee; border: 1px solid #f44336; padding: 15px; margin: 20px 0; border-radius: 5px; }\\n        table { border-collapse: collapse; width: 100%; margin-top: 20px; background: white; }\\n        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }\\n        th { background-color: #f2f2f2; font-weight: bold; }\\n        tr:nth-child(even) { background-color: #f9f9f9; }\\n        .footer { margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px; text-align: center; color: #666; }\\n    \u003c/style\u003e\\n\u003c/head\u003e\\n\u003cbody\u003e\\n    \u003cdiv class=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003e\\n\u003chead\u003e\\n    \u003cmeta charset=\\",
                               "Type":  "Other"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "\u003edata\\\\\\\\dns_reports\\\\\\\\setup\\\\\\\\dns_cache_output.txt\u003c/div\u003e\\n        \u003c/div\u003e\\n        \\n        ${data.fileStatus ? `\\n        \u003cdiv class=\\",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "data\\\\\\\\dns_reports\\\\\\\\setup\\\\\\\\dns_cache_output.txt\u003c/div\u003e\\n",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "data\\n",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "data\\n}];",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "data\\nconst",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "Scripts\\\\\\\\get_dns_cache.ps1\u0027",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "Scripts\\\\\\\\get_dns_cache.ps1\u0027,\\n",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "Scripts\\\\\\\\setup_dns_automation.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                               "ReferencedPath":  "Scripts\\\\\\\\setup_dns_monitoring.ps1\u0027\\n",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\get_dns_cache.ps1",
                               "ReferencedPath":  "data\\dns_reports\\setup",
                               "Type":  "Data"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\get_dns_cache.ps1",
                               "ReferencedPath":  "Scripts\\update_domain_history.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\DOMAIN_HISTORY_GUIDE.md",
                               "ReferencedPath":  ".\\Scripts\\query_domain_history.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\DOMAIN_HISTORY_GUIDE.md",
                               "ReferencedPath":  "Scripts\\get_dns_cache.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\DOMAIN_HISTORY_GUIDE.md",
                               "ReferencedPath":  "Scripts\\query_domain_history.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "data\\dns_reports\\setup\\DOMAIN_HISTORY_GUIDE.md",
                               "ReferencedPath":  "Scripts\\update_domain_history.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Documentation\\DNS_Security_Monitor_README.md",
                               "ReferencedPath":  "\n- Run: `.\\Scripts\\get_dns_cache.ps1`\n- Check: `data/dns_reports/setup/dns_cache_output.txt` exists\n- Verify: PowerShell execution policy allows scripts\n\n#### ",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Documentation\\DNS_Security_Monitor_README.md",
                               "ReferencedPath":  ".\\Scripts\\create_dns_reports_directory.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Documentation\\DNS_Security_Monitor_README.md",
                               "ReferencedPath":  ".\\Scripts\\dns_cache_loop.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Documentation\\DNS_Security_Monitor_README.md",
                               "ReferencedPath":  ".\\Scripts\\fix_permissions.ps1`",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Documentation\\DNS_Security_Monitor_README.md",
                               "ReferencedPath":  ".\\Scripts\\get_dns_cache.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Documentation\\DNS_Security_Monitor_README.md",
                               "ReferencedPath":  ".\\Scripts\\get_dns_cache.ps1`",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Documentation\\DNS_Security_Monitor_README.md",
                               "ReferencedPath":  ".\\Scripts\\query_domain_history.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Documentation\\DNS_Security_Monitor_README.md",
                               "ReferencedPath":  ".\\Scripts\\setup_dns_automation.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Documentation\\DNS_Security_Monitor_README.md",
                               "ReferencedPath":  ".\\Scripts\\setup_dns_monitoring.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Documentation\\DNS_Security_Monitor_README.md",
                               "ReferencedPath":  "Scripts\\create_dns_reports_directory.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Documentation\\DNS_Security_Monitor_README.md",
                               "ReferencedPath":  "Scripts\\dns_cache_loop.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Documentation\\DNS_Security_Monitor_README.md",
                               "ReferencedPath":  "Scripts\\fix_permissions.ps1`",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Documentation\\DNS_Security_Monitor_README.md",
                               "ReferencedPath":  "Scripts\\get_dns_cache.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Documentation\\DNS_Security_Monitor_README.md",
                               "ReferencedPath":  "Scripts\\get_dns_cache.ps1`",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Documentation\\DNS_Security_Monitor_README.md",
                               "ReferencedPath":  "Scripts\\query_domain_history.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Documentation\\DNS_Security_Monitor_README.md",
                               "ReferencedPath":  "Scripts\\setup_dns_automation.ps1",
                               "Type":  "Script"
                           },
                           {
                               "SourceFile":  "Documentation\\DNS_Security_Monitor_README.md",
                               "ReferencedPath":  "Scripts\\setup_dns_monitoring.ps1",
                               "Type":  "Script"
                           }
                       ],
    "PathUpdates":  [
                        {
                            "File":  "Scripts\\analyze_dns_project_files.ps1",
                            "OldPath":  "1. Move DNS-related scripts from Scripts\\ to data\\dns_reports\\setup\\",
                            "NewPath":  "1. Move DNS-related scripts from data\\dns_reports\\setup\\ to data\\dns_reports\\setup\\"
                        },
                        {
                            "File":  "Scripts\\analyze_dns_project_files.ps1",
                            "OldPath":  "Analysis saved to: Scripts\\dns_project_analysis.json",
                            "NewPath":  "Analysis saved to: data\\dns_reports\\setup\\dns_project_analysis.json"
                        },
                        {
                            "File":  "Scripts\\analyze_dns_project_files.ps1",
                            "OldPath":  "Scripts\\\\.*dns",
                            "NewPath":  "data\\dns_reports\\setup\\\\.*dns"
                        },
                        {
                            "File":  "Scripts\\analyze_dns_project_files.ps1",
                            "OldPath":  "Scripts\\\\.*domain",
                            "NewPath":  "data\\dns_reports\\setup\\\\.*domain"
                        },
                        {
                            "File":  "Scripts\\analyze_dns_project_files.ps1",
                            "OldPath":  "Scripts\\dns_project_analysis.json",
                            "NewPath":  "data\\dns_reports\\setup\\dns_project_analysis.json"
                        },
                        {
                            "File":  "Scripts\\setup_dns_automation.ps1",
                            "OldPath":  "   Remove:  .\\Scripts\\setup_dns_automation.ps1 -Remove",
                            "NewPath":  "   Remove:  .\\data\\dns_reports\\setup\\setup_dns_automation.ps1 -Remove"
                        },
                        {
                            "File":  "Scripts\\setup_dns_automation.ps1",
                            "OldPath":  "   Status:  .\\Scripts\\setup_dns_automation.ps1 -Status",
                            "NewPath":  "   Status:  .\\data\\dns_reports\\setup\\setup_dns_automation.ps1 -Status"
                        },
                        {
                            "File":  "Scripts\\setup_dns_automation.ps1",
                            "OldPath":  ".\\Scripts\\setup_dns_automation.ps1",
                            "NewPath":  ".\\data\\dns_reports\\setup\\setup_dns_automation.ps1"
                        },
                        {
                            "File":  "Scripts\\setup_dns_automation.ps1",
                            "OldPath":  "Scripts\\setup_dns_automation.ps1",
                            "NewPath":  "data\\dns_reports\\setup\\setup_dns_automation.ps1"
                        },
                        {
                            "File":  "Scripts\\setup_dns_monitoring.ps1",
                            "OldPath":  "   - Run Scripts\\get_dns_cache.ps1 manually",
                            "NewPath":  "   - Run data\\dns_reports\\setup\\get_dns_cache.ps1 manually"
                        },
                        {
                            "File":  "Scripts\\setup_dns_monitoring.ps1",
                            "OldPath":  "   .\\Scripts\\get_dns_cache.ps1",
                            "NewPath":  "   .\\data\\dns_reports\\setup\\get_dns_cache.ps1"
                        },
                        {
                            "File":  "Scripts\\setup_dns_monitoring.ps1",
                            "OldPath":  "   Check status: .\\\\Scripts\\\\setup_dns_automation.ps1 -Status",
                            "NewPath":  "   Check status: .\\\\data\\dns_reports\\setup\\\\setup_dns_automation.ps1 -Status"
                        },
                        {
                            "File":  "Scripts\\setup_dns_monitoring.ps1",
                            "OldPath":  "   Expected files: Scripts\\get_dns_cache.ps1",
                            "NewPath":  "   Expected files: data\\dns_reports\\setup\\get_dns_cache.ps1"
                        },
                        {
                            "File":  "Scripts\\setup_dns_monitoring.ps1",
                            "OldPath":  ".\\Scripts\\get_dns_cache.ps1\"",
                            "NewPath":  ".\\data\\dns_reports\\setup\\get_dns_cache.ps1\""
                        },
                        {
                            "File":  "Scripts\\setup_dns_monitoring.ps1",
                            "OldPath":  "Scripts\\\\setup_dns_automation.ps1",
                            "NewPath":  "data\\dns_reports\\setup\\\\setup_dns_automation.ps1"
                        },
                        {
                            "File":  "Scripts\\setup_dns_monitoring.ps1",
                            "OldPath":  "Scripts\\dns_cache_loop.ps1",
                            "NewPath":  "data\\dns_reports\\setup\\dns_cache_loop.ps1"
                        },
                        {
                            "File":  "Scripts\\setup_dns_monitoring.ps1",
                            "OldPath":  "Scripts\\get_dns_cache.ps1",
                            "NewPath":  "data\\dns_reports\\setup\\get_dns_cache.ps1"
                        },
                        {
                            "File":  "Scripts\\setup_dns_monitoring.ps1",
                            "OldPath":  "Scripts\\setup_dns_automation.ps1",
                            "NewPath":  "data\\dns_reports\\setup\\setup_dns_automation.ps1"
                        },
                        {
                            "File":  "Scripts\\query_domain_history.ps1",
                            "OldPath":  "  .\\Scripts\\query_domain_history.ps1 -Query export -RiskLevel HIGH",
                            "NewPath":  "  .\\data\\dns_reports\\setup\\query_domain_history.ps1 -Query export -RiskLevel HIGH"
                        },
                        {
                            "File":  "Scripts\\query_domain_history.ps1",
                            "OldPath":  "  .\\Scripts\\query_domain_history.ps1 -Query recent -Days 7",
                            "NewPath":  "  .\\data\\dns_reports\\setup\\query_domain_history.ps1 -Query recent -Days 7"
                        },
                        {
                            "File":  "Scripts\\query_domain_history.ps1",
                            "OldPath":  "  .\\Scripts\\query_domain_history.ps1 -Query search -Domain \u0027google\u0027",
                            "NewPath":  "  .\\data\\dns_reports\\setup\\query_domain_history.ps1 -Query search -Domain \u0027google\u0027"
                        },
                        {
                            "File":  "Scripts\\query_domain_history.ps1",
                            "OldPath":  "  .\\Scripts\\query_domain_history.ps1 -Query summary",
                            "NewPath":  "  .\\data\\dns_reports\\setup\\query_domain_history.ps1 -Query summary"
                        },
                        {
                            "File":  "Scripts\\query_domain_history.ps1",
                            "OldPath":  ".\\Scripts\\query_domain_history.ps1",
                            "NewPath":  ".\\data\\dns_reports\\setup\\query_domain_history.ps1"
                        },
                        {
                            "File":  "Scripts\\query_domain_history.ps1",
                            "OldPath":  "Scripts\\query_domain_history.ps1",
                            "NewPath":  "data\\dns_reports\\setup\\query_domain_history.ps1"
                        },
                        {
                            "File":  "data\\dns_reports\\setup\\collect_dns_cache.bat",
                            "OldPath":  "Scripts\\get_dns_cache.ps1",
                            "NewPath":  "data\\dns_reports\\setup\\get_dns_cache.ps1"
                        },
                        {
                            "File":  "data\\dns_reports\\setup\\collect_dns_cache.ps1",
                            "OldPath":  "Cannot find Scripts\\get_dns_cache.ps1 in $projectRoot",
                            "NewPath":  "Cannot find data\\dns_reports\\setup\\get_dns_cache.ps1 in $projectRoot"
                        },
                        {
                            "File":  "data\\dns_reports\\setup\\collect_dns_cache.ps1",
                            "OldPath":  "Scripts\\get_dns_cache.ps1",
                            "NewPath":  "data\\dns_reports\\setup\\get_dns_cache.ps1"
                        },
                        {
                            "File":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                            "OldPath":  "// Validate DNS cache file freshness and stop if stale\\nconst input = $input.first().json;\\nconst fileCheckOutput = input.stdout?.trim();\\n\\nconsole.log(\u0027=== DNS CACHE FILE FRESHNESS CHECK ===\u0027);\\nconsole.log(\u0027File check output:\u0027, fileCheckOutput);\\n\\nconst currentTime = Math.floor(Date.now() / 1000); // Unix timestamp\\nconst maxAgeMinutes = 65; // File must be newer than 65 minutes (Windows task runs every 60 min)\\nconst maxAgeSeconds = maxAgeMinutes * 60;\\n\\nlet fileStatus = {\\n  exists: false,\\n  ageMinutes: 0,\\n  isValid: false,\\n  error: null\\n};\\n\\nif (fileCheckOutput === \u0027FILE_NOT_FOUND\u0027 || !fileCheckOutput || fileCheckOutput.includes(\u0027No such file\u0027)) {\\n  fileStatus.error = \u0027DNS cache file does not exist\u0027;\\n  console.log(\u0027âŒ CRITICAL ERROR: DNS cache file not found\u0027);\\n  console.log(\u0027   Expected: /home/<USER>/shared/dns_reports/setup/dns_cache_output.txt\u0027);\\n  console.log(\u0027   This indicates Windows automation is not working\u0027);\\n  \\n} else {\\n  try {\\n    const fileTimestamp = parseInt(fileCheckOutput);\\n    const fileAge = currentTime - fileTimestamp;\\n    fileStatus.ageMinutes = Math.floor(fileAge / 60);\\n    fileStatus.exists = true;\\n    \\n    console.log(`File timestamp: ${fileTimestamp} (${new Date(fileTimestamp * 1000).toISOString()})`);\\n    console.log(`Current time: ${currentTime} (${new Date(currentTime * 1000).toISOString()})`);\\n    console.log(`File age: ${fileStatus.ageMinutes} minutes`);\\n    \\n    if (fileAge \u003c= maxAgeSeconds) {\\n      fileStatus.isValid = true;\\n      console.log(\u0027âœ… DNS cache file is fresh and valid\u0027);\\n    } else {\\n      fileStatus.error = `DNS cache file is too old (${fileStatus.ageMinutes} minutes, max ${maxAgeMinutes})`;\\n      console.log(`âŒ CRITICAL ERROR: DNS cache file is stale`);\\n      console.log(`   File age: ${fileStatus.ageMinutes} minutes (maximum allowed: ${maxAgeMinutes})`);\\n      console.log(`   This indicates Windows automation has stopped working`);\\n    }\\n  } catch (error) {\\n    fileStatus.error = `Error parsing file timestamp: ${error.message}`;\\n    console.log(\u0027âŒ CRITICAL ERROR: Cannot parse file timestamp\u0027);\\n    console.log(\u0027   Raw output:\u0027, fileCheckOutput);\\n  }\\n}\\n\\nif (!fileStatus.isValid) {\\n  console.log(\u0027\\\\nðŸš¨ AUTOMATION FAILURE DETECTED ðŸš¨\u0027);\\n  console.log(\u0027The DNS Security Monitor automation is not working properly.\u0027);\\n  console.log(\u0027\\\\nRequired Actions:\u0027);\\n  console.log(\u00271. Check Windows Task Scheduler: .\\\\\\\\Scripts\\\\\\\\setup_dns_automation.ps1 -Status\u0027);\\n  console.log(\u00272. Manually run: .\\\\\\\\Scripts\\\\\\\\get_dns_cache.ps1\u0027);\\n  console.log(\u00273. Verify Docker volume mount is working\u0027);\\n  console.log(\u00274. Check PowerShell execution policy\u0027);\\n  console.log(\u0027\\\\nWorkflow will generate ERROR REPORT instead of security analysis.\u0027);\\n  \\n  // Return error status that will trigger error report generation\\n  return [{\\n    json: {\\n      systemError: true,\\n      errorType: \u0027DNS_CACHE_AUTOMATION_FAILURE\u0027,\\n      errorMessage: fileStatus.error,\\n      fileStatus: fileStatus,\\n      troubleshooting: {\\n        checkTaskScheduler: \u0027.\\\\\\\\Scripts\\\\\\\\setup_dns_automation.ps1 -Status\u0027,\\n        manualCollection: \u0027.\\\\\\\\Scripts\\\\\\\\get_dns_cache.ps1\u0027,\\n        setupAutomation: \u0027.\\\\\\\\Scripts\\\\\\\\setup_dns_monitoring.ps1\u0027\\n      },\\n      timestamp: new Date().toISOString()\\n    }\\n  }];\\n}\\n\\n// File is valid, proceed with normal workflow\\nconsole.log(\u0027DNS cache file validation passed - proceeding with analysis\u0027);\\nreturn [{\\n  json: {\\n    systemError: false,\\n    fileStatus: fileStatus,\\n    validationPassed: true,\\n    timestamp: new Date().toISOString()\\n  }\\n}];",
                            "NewPath":  "// Validate DNS cache file freshness and stop if stale\\nconst input = $input.first().json;\\nconst fileCheckOutput = input.stdout?.trim();\\n\\nconsole.log(\u0027=== DNS CACHE FILE FRESHNESS CHECK ===\u0027);\\nconsole.log(\u0027File check output:\u0027, fileCheckOutput);\\n\\nconst currentTime = Math.floor(Date.now() / 1000); // Unix timestamp\\nconst maxAgeMinutes = 65; // File must be newer than 65 minutes (Windows task runs every 60 min)\\nconst maxAgeSeconds = maxAgeMinutes * 60;\\n\\nlet fileStatus = {\\n  exists: false,\\n  ageMinutes: 0,\\n  isValid: false,\\n  error: null\\n};\\n\\nif (fileCheckOutput === \u0027FILE_NOT_FOUND\u0027 || !fileCheckOutput || fileCheckOutput.includes(\u0027No such file\u0027)) {\\n  fileStatus.error = \u0027DNS cache file does not exist\u0027;\\n  console.log(\u0027âŒ CRITICAL ERROR: DNS cache file not found\u0027);\\n  console.log(\u0027   Expected: /home/<USER>/shared/dns_reports/setup/dns_cache_output.txt\u0027);\\n  console.log(\u0027   This indicates Windows automation is not working\u0027);\\n  \\n} else {\\n  try {\\n    const fileTimestamp = parseInt(fileCheckOutput);\\n    const fileAge = currentTime - fileTimestamp;\\n    fileStatus.ageMinutes = Math.floor(fileAge / 60);\\n    fileStatus.exists = true;\\n    \\n    console.log(`File timestamp: ${fileTimestamp} (${new Date(fileTimestamp * 1000).toISOString()})`);\\n    console.log(`Current time: ${currentTime} (${new Date(currentTime * 1000).toISOString()})`);\\n    console.log(`File age: ${fileStatus.ageMinutes} minutes`);\\n    \\n    if (fileAge \u003c= maxAgeSeconds) {\\n      fileStatus.isValid = true;\\n      console.log(\u0027âœ… DNS cache file is fresh and valid\u0027);\\n    } else {\\n      fileStatus.error = `DNS cache file is too old (${fileStatus.ageMinutes} minutes, max ${maxAgeMinutes})`;\\n      console.log(`âŒ CRITICAL ERROR: DNS cache file is stale`);\\n      console.log(`   File age: ${fileStatus.ageMinutes} minutes (maximum allowed: ${maxAgeMinutes})`);\\n      console.log(`   This indicates Windows automation has stopped working`);\\n    }\\n  } catch (error) {\\n    fileStatus.error = `Error parsing file timestamp: ${error.message}`;\\n    console.log(\u0027âŒ CRITICAL ERROR: Cannot parse file timestamp\u0027);\\n    console.log(\u0027   Raw output:\u0027, fileCheckOutput);\\n  }\\n}\\n\\nif (!fileStatus.isValid) {\\n  console.log(\u0027\\\\nðŸš¨ AUTOMATION FAILURE DETECTED ðŸš¨\u0027);\\n  console.log(\u0027The DNS Security Monitor automation is not working properly.\u0027);\\n  console.log(\u0027\\\\nRequired Actions:\u0027);\\n  console.log(\u00271. Check Windows Task Scheduler: .\\\\\\\\data\\dns_reports\\setup\\\\\\\\setup_dns_automation.ps1 -Status\u0027);\\n  console.log(\u00272. Manually run: .\\\\\\\\data\\dns_reports\\setup\\\\\\\\get_dns_cache.ps1\u0027);\\n  console.log(\u00273. Verify Docker volume mount is working\u0027);\\n  console.log(\u00274. Check PowerShell execution policy\u0027);\\n  console.log(\u0027\\\\nWorkflow will generate ERROR REPORT instead of security analysis.\u0027);\\n  \\n  // Return error status that will trigger error report generation\\n  return [{\\n    json: {\\n      systemError: true,\\n      errorType: \u0027DNS_CACHE_AUTOMATION_FAILURE\u0027,\\n      errorMessage: fileStatus.error,\\n      fileStatus: fileStatus,\\n      troubleshooting: {\\n        checkTaskScheduler: \u0027.\\\\\\\\data\\dns_reports\\setup\\\\\\\\setup_dns_automation.ps1 -Status\u0027,\\n        manualCollection: \u0027.\\\\\\\\data\\dns_reports\\setup\\\\\\\\get_dns_cache.ps1\u0027,\\n        setupAutomation: \u0027.\\\\\\\\data\\dns_reports\\setup\\\\\\\\setup_dns_monitoring.ps1\u0027\\n      },\\n      timestamp: new Date().toISOString()\\n    }\\n  }];\\n}\\n\\n// File is valid, proceed with normal workflow\\nconsole.log(\u0027DNS cache file validation passed - proceeding with analysis\u0027);\\nreturn [{\\n  json: {\\n    systemError: false,\\n    fileStatus: fileStatus,\\n    validationPassed: true,\\n    timestamp: new Date().toISOString()\\n  }\\n}];"
                        },
                        {
                            "File":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                            "OldPath":  "Scripts\\\\\\\\get_dns_cache.ps1\u0027",
                            "NewPath":  "data\\dns_reports\\setup\\\\\\\\get_dns_cache.ps1\u0027"
                        },
                        {
                            "File":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                            "OldPath":  "Scripts\\\\\\\\get_dns_cache.ps1\u0027,\\n",
                            "NewPath":  "data\\dns_reports\\setup\\\\\\\\get_dns_cache.ps1\u0027,\\n"
                        },
                        {
                            "File":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                            "OldPath":  "Scripts\\\\\\\\setup_dns_automation.ps1",
                            "NewPath":  "data\\dns_reports\\setup\\\\\\\\setup_dns_automation.ps1"
                        },
                        {
                            "File":  "data\\dns_reports\\setup\\dns_security_workflow_fixed.json",
                            "OldPath":  "Scripts\\\\\\\\setup_dns_monitoring.ps1\u0027\\n",
                            "NewPath":  "data\\dns_reports\\setup\\\\\\\\setup_dns_monitoring.ps1\u0027\\n"
                        },
                        {
                            "File":  "data\\dns_reports\\setup\\get_dns_cache.ps1",
                            "OldPath":  "Scripts\\update_domain_history.ps1",
                            "NewPath":  "data\\dns_reports\\setup\\update_domain_history.ps1"
                        },
                        {
                            "File":  "data\\dns_reports\\setup\\DOMAIN_HISTORY_GUIDE.md",
                            "OldPath":  ".\\Scripts\\query_domain_history.ps1",
                            "NewPath":  ".\\data\\dns_reports\\setup\\query_domain_history.ps1"
                        },
                        {
                            "File":  "data\\dns_reports\\setup\\DOMAIN_HISTORY_GUIDE.md",
                            "OldPath":  "Scripts\\get_dns_cache.ps1",
                            "NewPath":  "data\\dns_reports\\setup\\get_dns_cache.ps1"
                        },
                        {
                            "File":  "data\\dns_reports\\setup\\DOMAIN_HISTORY_GUIDE.md",
                            "OldPath":  "Scripts\\query_domain_history.ps1",
                            "NewPath":  "data\\dns_reports\\setup\\query_domain_history.ps1"
                        },
                        {
                            "File":  "data\\dns_reports\\setup\\DOMAIN_HISTORY_GUIDE.md",
                            "OldPath":  "Scripts\\update_domain_history.ps1",
                            "NewPath":  "data\\dns_reports\\setup\\update_domain_history.ps1"
                        },
                        {
                            "File":  "Documentation\\DNS_Security_Monitor_README.md",
                            "OldPath":  "\n- Run: `.\\Scripts\\get_dns_cache.ps1`\n- Check: `data/dns_reports/setup/dns_cache_output.txt` exists\n- Verify: PowerShell execution policy allows scripts\n\n#### ",
                            "NewPath":  "\n- Run: `.\\data\\dns_reports\\setup\\get_dns_cache.ps1`\n- Check: `data/dns_reports/setup/dns_cache_output.txt` exists\n- Verify: PowerShell execution policy allows scripts\n\n#### "
                        },
                        {
                            "File":  "Documentation\\DNS_Security_Monitor_README.md",
                            "OldPath":  ".\\Scripts\\create_dns_reports_directory.ps1",
                            "NewPath":  ".\\data\\dns_reports\\setup\\create_dns_reports_directory.ps1"
                        },
                        {
                            "File":  "Documentation\\DNS_Security_Monitor_README.md",
                            "OldPath":  ".\\Scripts\\dns_cache_loop.ps1",
                            "NewPath":  ".\\data\\dns_reports\\setup\\dns_cache_loop.ps1"
                        },
                        {
                            "File":  "Documentation\\DNS_Security_Monitor_README.md",
                            "OldPath":  ".\\Scripts\\get_dns_cache.ps1",
                            "NewPath":  ".\\data\\dns_reports\\setup\\get_dns_cache.ps1"
                        },
                        {
                            "File":  "Documentation\\DNS_Security_Monitor_README.md",
                            "OldPath":  ".\\Scripts\\get_dns_cache.ps1`",
                            "NewPath":  ".\\data\\dns_reports\\setup\\get_dns_cache.ps1`"
                        },
                        {
                            "File":  "Documentation\\DNS_Security_Monitor_README.md",
                            "OldPath":  ".\\Scripts\\query_domain_history.ps1",
                            "NewPath":  ".\\data\\dns_reports\\setup\\query_domain_history.ps1"
                        },
                        {
                            "File":  "Documentation\\DNS_Security_Monitor_README.md",
                            "OldPath":  ".\\Scripts\\setup_dns_automation.ps1",
                            "NewPath":  ".\\data\\dns_reports\\setup\\setup_dns_automation.ps1"
                        },
                        {
                            "File":  "Documentation\\DNS_Security_Monitor_README.md",
                            "OldPath":  ".\\Scripts\\setup_dns_monitoring.ps1",
                            "NewPath":  ".\\data\\dns_reports\\setup\\setup_dns_monitoring.ps1"
                        },
                        {
                            "File":  "Documentation\\DNS_Security_Monitor_README.md",
                            "OldPath":  "Scripts\\create_dns_reports_directory.ps1",
                            "NewPath":  "data\\dns_reports\\setup\\create_dns_reports_directory.ps1"
                        },
                        {
                            "File":  "Documentation\\DNS_Security_Monitor_README.md",
                            "OldPath":  "Scripts\\dns_cache_loop.ps1",
                            "NewPath":  "data\\dns_reports\\setup\\dns_cache_loop.ps1"
                        },
                        {
                            "File":  "Documentation\\DNS_Security_Monitor_README.md",
                            "OldPath":  "Scripts\\get_dns_cache.ps1",
                            "NewPath":  "data\\dns_reports\\setup\\get_dns_cache.ps1"
                        },
                        {
                            "File":  "Documentation\\DNS_Security_Monitor_README.md",
                            "OldPath":  "Scripts\\get_dns_cache.ps1`",
                            "NewPath":  "data\\dns_reports\\setup\\get_dns_cache.ps1`"
                        },
                        {
                            "File":  "Documentation\\DNS_Security_Monitor_README.md",
                            "OldPath":  "Scripts\\query_domain_history.ps1",
                            "NewPath":  "data\\dns_reports\\setup\\query_domain_history.ps1"
                        },
                        {
                            "File":  "Documentation\\DNS_Security_Monitor_README.md",
                            "OldPath":  "Scripts\\setup_dns_automation.ps1",
                            "NewPath":  "data\\dns_reports\\setup\\setup_dns_automation.ps1"
                        },
                        {
                            "File":  "Documentation\\DNS_Security_Monitor_README.md",
                            "OldPath":  "Scripts\\setup_dns_monitoring.ps1",
                            "NewPath":  "data\\dns_reports\\setup\\setup_dns_monitoring.ps1"
                        }
                    ]
}
