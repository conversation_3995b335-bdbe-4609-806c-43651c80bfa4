{"name": "Simple File Write Test", "nodes": [{"parameters": {}, "id": "manual-trigger", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"jsCode": "// Create simple test content following <PERSON><PERSON><PERSON>'s advice\nconst testContent = `Simple File Write Test\nTimestamp: ${new Date().toISOString()}\nThis file should appear in ./data/ directory\nTest successful!\n`;\n\nconsole.log('=== SIMPLE FILE WRITE TEST ===');\nconsole.log('Content prepared:', testContent.length, 'characters');\n\n// Convert to binary data for Write Binary File node\nconst binaryData = {\n  data: Buffer.from(testContent, 'utf8').toString('base64'),\n  mimeType: 'text/plain',\n  fileName: 'simple_test.txt',\n  fileExtension: 'txt'\n};\n\nconsole.log('Binary data prepared for Write Binary File node');\nconsole.log('Target file: /home/<USER>/shared/simple_test.txt');\nconsole.log('Host path: ./data/simple_test.txt');\n\nreturn [{\n  json: {\n    testContent: testContent,\n    fileName: 'simple_test.txt',\n    timestamp: new Date().toISOString()\n  },\n  binary: {\n    data: binaryData\n  }\n}];"}, "id": "prepare-simple-test", "name": "Prepare Simple Test", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"fileName": "/home/<USER>/shared/simple_test.txt", "dataPropertyName": "data", "options": {"overwrite": true}}, "id": "write-simple-file", "name": "Write Simple File", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [680, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Prepare Simple Test", "type": "main", "index": 0}]]}, "Prepare Simple Test": {"main": [[{"node": "Write Simple File", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 1, "updatedAt": "2025-08-15T23:45:00.000Z", "versionId": "1"}