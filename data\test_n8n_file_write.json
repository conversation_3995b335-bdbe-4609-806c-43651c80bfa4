{"name": "Test N8N File Writing", "nodes": [{"parameters": {}, "id": "manual-trigger", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"jsCode": "// Test file writing capabilities\nconsole.log('=== N8N FILE WRITING TEST ===');\n\n// Create test content\nconst testContent = `\nN8N File Writing Test\nTimestamp: ${new Date().toISOString()}\nTest successful!\n`;\n\nconsole.log('Test content created:', testContent);\n\n// Convert to binary data for Write Binary File node\nconst binaryData = {\n  data: Buffer.from(testContent, 'utf8').toString('base64'),\n  mimeType: 'text/plain',\n  fileName: 'n8n_test_file.txt',\n  fileExtension: 'txt'\n};\n\nconsole.log('Binary data prepared:');\nconsole.log('- Data length:', binaryData.data.length);\nconsole.log('- MIME type:', binaryData.mimeType);\nconsole.log('- File name:', binaryData.fileName);\n\nreturn [{\n  json: {\n    testContent: testContent,\n    fileName: binaryData.fileName,\n    timestamp: new Date().toISOString()\n  },\n  binary: {\n    data: binaryData\n  }\n}];"}, "id": "prepare-test-data", "name": "Prepare Test Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"fileName": "={{ $json.fileName }}", "dataPropertyName": "data", "options": {"overwrite": true}}, "id": "write-test-file", "name": "Write Test File", "type": "n8n-nodes-base.writeBinaryFile", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"jsCode": "// Verify file was written\nconst data = $input.first().json;\n\nconsole.log('=== FILE WRITE VERIFICATION ===');\nconsole.log('Expected file:', data.fileName);\nconsole.log('Timestamp:', data.timestamp);\n\n// Check if we can access the shared directory\ntry {\n  // This will help us understand the container's file system\n  console.log('Container working directory:', process.cwd());\n  console.log('Container environment:');\n  console.log('- NODE_ENV:', process.env.NODE_ENV);\n  console.log('- HOME:', process.env.HOME);\n  \n  // List available directories\n  const fs = require('fs');\n  if (fs.existsSync('/home/<USER>/shared')) {\n    console.log('✅ /home/<USER>/shared directory exists');\n    const files = fs.readdirSync('/home/<USER>/shared');\n    console.log('Files in shared directory:', files);\n  } else {\n    console.log('❌ /home/<USER>/shared directory does not exist');\n  }\n  \n} catch (error) {\n  console.log('Error checking file system:', error.message);\n}\n\nreturn [{\n  json: {\n    ...data,\n    verificationComplete: true,\n    verificationTime: new Date().toISOString()\n  }\n}];"}, "id": "verify-file-write", "name": "Verify File Write", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 300]}], "connections": {"Manual Trigger": {"main": [[{"node": "Prepare Test Data", "type": "main", "index": 0}]]}, "Prepare Test Data": {"main": [[{"node": "Write Test File", "type": "main", "index": 0}]]}, "Write Test File": {"main": [[{"node": "Verify File Write", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 1, "updatedAt": "2025-08-15T22:00:00.000Z", "versionId": "1"}