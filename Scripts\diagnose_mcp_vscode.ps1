# Diagnose MCP Integration with VS Code
# This script checks why MCP servers aren't appearing in VS Code output dropdown

Write-Host "MCP VS Code Integration Diagnostic" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan

# Check 1: VS Code version and MCP support
Write-Host "`n1. VS Code Version Check" -ForegroundColor Yellow
try {
    $vscodeVersion = & code --version 2>$null | Select-Object -First 1
    Write-Host "VS Code version: $vscodeVersion" -ForegroundColor Green
    
    if ($vscodeVersion -match "^(\d+)\.(\d+)") {
        $major = [int]$matches[1]
        $minor = [int]$matches[2]
        
        if ($major -eq 1 -and $minor -ge 102) {
            Write-Host "✅ VS Code supports MCP (1.102+)" -ForegroundColor Green
        } else {
            Write-Host "❌ VS Code version too old for MCP (requires 1.102+)" -ForegroundColor Red
            exit 1
        }
    }
} catch {
    Write-Host "❌ Could not check VS Code version" -ForegroundColor Red
    exit 1
}

# Check 2: MCP configuration file
Write-Host "`n2. MCP Configuration Check" -ForegroundColor Yellow
$mcpConfigPath = ".vscode\mcp.json"
if (Test-Path $mcpConfigPath) {
    Write-Host "✅ MCP configuration found: $mcpConfigPath" -ForegroundColor Green
    
    try {
        $mcpConfig = Get-Content $mcpConfigPath | ConvertFrom-Json
        $serverCount = $mcpConfig.servers.PSObject.Properties.Count
        Write-Host "   Configured servers: $serverCount" -ForegroundColor White
        
        foreach ($serverName in $mcpConfig.servers.PSObject.Properties.Name) {
            Write-Host "   - $serverName" -ForegroundColor Gray
        }
    } catch {
        Write-Host "⚠️  Could not parse MCP configuration" -ForegroundColor Yellow
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "❌ MCP configuration not found: $mcpConfigPath" -ForegroundColor Red
    exit 1
}

# Check 3: Virtual environment and Python path
Write-Host "`n3. Python Environment Check" -ForegroundColor Yellow
$venvPath = ".\venv\Scripts\python.exe"
if (Test-Path $venvPath) {
    Write-Host "✅ Virtual environment found" -ForegroundColor Green
    
    try {
        $pythonVersion = & $venvPath --version 2>$null
        Write-Host "   Python version: $pythonVersion" -ForegroundColor White
        
        # Check if MCP package is installed
        $mcpCheck = & $venvPath -c "import mcp; print('MCP package available')" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✅ MCP package installed" -ForegroundColor Green
        } else {
            Write-Host "   ❌ MCP package not found" -ForegroundColor Red
        }
    } catch {
        Write-Host "   ⚠️  Could not check Python environment" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Virtual environment not found: $venvPath" -ForegroundColor Red
}

# Check 4: Test MCP server modules
Write-Host "`n4. MCP Server Module Check" -ForegroundColor Yellow
$servers = @(
    "n8n_builder.mcp_research_server",
    "n8n_builder.mcp_database_server", 
    "n8n_builder.mcp_workflow_server"
)

foreach ($server in $servers) {
    try {
        $testResult = & $venvPath -c "
import sys
sys.path.insert(0, '.')
try:
    import $server
    print('OK')
except Exception as e:
    print(f'ERROR: {e}')
" 2>&1
        
        if ($testResult -match "OK") {
            Write-Host "   ✅ $server" -ForegroundColor Green
        } else {
            Write-Host "   ❌ $server - $testResult" -ForegroundColor Red
        }
    } catch {
        Write-Host "   ❌ $server - Failed to test" -ForegroundColor Red
    }
}

# Check 5: VS Code settings for MCP
Write-Host "`n5. VS Code Settings Check" -ForegroundColor Yellow
$vscodeSettingsPath = ".vscode\settings.json"
if (Test-Path $vscodeSettingsPath) {
    Write-Host "✅ VS Code settings found" -ForegroundColor Green
    
    try {
        $settings = Get-Content $vscodeSettingsPath | ConvertFrom-Json
        
        # Check for MCP-related settings
        $mcpSettings = @()
        if ($settings."chat.mcp.autostart") {
            $mcpSettings += "chat.mcp.autostart: $($settings.'chat.mcp.autostart')"
        }
        if ($settings."chat.mcp.discovery.enabled") {
            $mcpSettings += "chat.mcp.discovery.enabled: $($settings.'chat.mcp.discovery.enabled')"
        }
        
        if ($mcpSettings.Count -gt 0) {
            Write-Host "   MCP settings found:" -ForegroundColor White
            $mcpSettings | ForEach-Object { Write-Host "   - $_" -ForegroundColor Gray }
        } else {
            Write-Host "   No MCP-specific settings found" -ForegroundColor Gray
        }
    } catch {
        Write-Host "   ⚠️  Could not parse VS Code settings" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠️  VS Code settings not found (this is optional)" -ForegroundColor Yellow
}

# Check 6: Process check for running MCP servers
Write-Host "`n6. Running Process Check" -ForegroundColor Yellow
$pythonProcesses = Get-Process | Where-Object { $_.ProcessName -like "*python*" }
if ($pythonProcesses) {
    Write-Host "✅ Python processes found:" -ForegroundColor Green
    $pythonProcesses | ForEach-Object { 
        Write-Host "   - PID: $($_.Id), Name: $($_.ProcessName)" -ForegroundColor Gray 
    }
} else {
    Write-Host "⚠️  No Python processes currently running" -ForegroundColor Yellow
}

# Recommendations
Write-Host "`n7. Diagnostic Results & Recommendations" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan

Write-Host "`nMost likely causes for MCP not appearing in VS Code output:" -ForegroundColor White
Write-Host "1. MCP servers haven't been started by VS Code yet" -ForegroundColor Yellow
Write-Host "2. VS Code needs to be restarted after MCP configuration" -ForegroundColor Yellow
Write-Host "3. GitHub Copilot extension may not be properly enabled" -ForegroundColor Yellow
Write-Host "4. MCP servers are failing to start (check logs)" -ForegroundColor Yellow

Write-Host "`nNext steps to try:" -ForegroundColor White
Write-Host "1. Completely close and restart VS Code" -ForegroundColor Gray
Write-Host "2. Open Chat view (Ctrl+Alt+I) and enable Agent mode" -ForegroundColor Gray
Write-Host "3. Look for MCP servers in Extensions view > MCP SERVERS section" -ForegroundColor Gray
Write-Host "4. Check VS Code Output panel for any MCP error messages" -ForegroundColor Gray
Write-Host "5. Try manually starting an MCP server to test" -ForegroundColor Gray

Write-Host "`nManual MCP server test command:" -ForegroundColor White
Write-Host ".\venv\Scripts\python.exe -m n8n_builder.mcp_research_server" -ForegroundColor Cyan

Write-Host "`nDiagnostic completed!" -ForegroundColor Green
